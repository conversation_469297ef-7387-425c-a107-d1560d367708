<template>
  <div class="login-form-box-pass">
    <div class="forgetText">密码至少在8位以上，大小写字母、数字、符号至少包含3种。</div>
    <el-form :model="forgetForm" :rules="forgetrules" class="login-form" ref="forgetForm" @submit.native.prevent>
      <el-form-item prop="account" class="login-form-input">
        <el-input v-model="forgetForm.account" placeholder="账号/手机号/证件号码" clearable></el-input>
      </el-form-item>

      <el-form-item class="login-form-input form-valid">
        <el-input v-model="forgetForm.validCode" placeholder="验证码" clearable> </el-input>
        <valid :length="4" :heigth="'36px'" :value.sync="validCode"></valid>
      </el-form-item>
      <!-- <div class="form-valid">
        <el-input
          v-model="forgetForm.validCode"
          placeholder="验证码"
          clearable
        > </el-input>
        <valid
          :length="4"
          :heigth="'36px'"
          :value.sync="validCode"
        ></valid>
      </div> -->
      <el-form-item prop="newPassword" class="login-form-input">

        <el-input type="password" v-model="forgetForm.newPassword" placeholder="请输入新密码" clearable></el-input>
      </el-form-item>

      <el-form-item prop="newPassword" class="login-form-input">

        <el-input type="password" v-model="forgetForm.newPasswordAgian" placeholder="请再次输入新密码" clearable></el-input>
      </el-form-item>
      <el-form-item class="login-form-input form-valid">
        <el-input v-model="forgetForm.smsValidation" placeholder="短信验证码" clearable> </el-input>
        <el-button type="primary" @click="validCodetesting" :disabled="smsText != '获取验证码'"> {{ smsText }} </el-button>
      </el-form-item>
      <div class="login-form-operation">
        <div class="login-form-operation-box">
          <div class="login-form-operation-text" @click="gologin">返回登录</div>
        </div>
      </div>
      <el-button type="primary" class="login-form-button" @click="editpwd('forgetForm')"> 确定 </el-button>
    </el-form>

  </div>
</template>

<script>
import valid from '@/components/valid/valid'
export default {
  components: { valid },
  data () {
    return {
      validCode: '',
      forgetForm: {
        account: '',
        validCode: '',
        smsValidation: '',
        newPassword: '',
        newPasswordAgian: ''
      },
      forgetrules: {
        account: [
          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        newPasswordAgian: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        smsValidation:
          [{ required: true, message: '请输入短信验证码', trigger: 'blur' }]
      },
      smsShow: false,
      smsText: '获取验证码',
      countdown: 0
    }
  },
  methods: {
    // 密码验证函数：8位以上，大小写字母、数字、符号至少包含3种
    validatePassword (password) {
      if (password.length < 8) {
        return false
      }

      let typeCount = 0

      // 检查是否包含小写字母
      if (/[a-z]/.test(password)) {
        typeCount++
      }

      // 检查是否包含大写字母
      if (/[A-Z]/.test(password)) {
        typeCount++
      }

      // 检查是否包含数字
      if (/[0-9]/.test(password)) {
        typeCount++
      }

      // 检查是否包含符号
      if (/[~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        typeCount++
      }

      return typeCount >= 3
    },

    forget () {
      // this.$router.push({ name: 'changepassword' })
      this.forgetflag = true
      this.forgetForm.account = this.loginForm.account
      this.loginForm.smsValidation = ''
      this.countdown = 0
    },
    gologin () {
      // this.countdown = 0
      // this.forgetflag = false
      this.$emit('gologin')
    },
    validCodetesting () {
      if (this.forgetForm.account === '') {
        this.$message({
          message: '请先输入账号/手机号/证件号码！',
          type: 'warning'
        })
        return
      }
      if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {
        this.forgetForm.validCode = ''
        this.$message.warning('验证码不正确!')
        return
      }
      if (!this.validatePassword(this.forgetForm.newPassword)) {
        this.$message({
          message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',
          type: 'warning'
        })
        return
      }

      if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {
        this.$message({
          message: '两次密码输入不一致！',
          type: 'warning'
        })
        return
      }
      this.$api.general.sendmessage({
        account: this.forgetForm.account,
        password: ''
      }).then(res => {
        this.countdown = 60
        this.settime()
        this.$message({
          message: '短信发送成功!',
          type: 'success'
        })
      })
    },
    editpwd (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {
            this.forgetForm.validCode = ''
            this.$message.warning('验证码不正确!')
            return
          }
          if (!this.validatePassword(this.forgetForm.newPassword)) {
            this.$message({
              message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',
              type: 'warning'
            })
            return
          }

          if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {
            this.$message({
              message: '两次密码输入不一致！',
              type: 'warning'
            })
            return
          }
          if (this.forgetForm.smsValidation === '') {
            this.$message({
              message: '请输入验证码！',
              type: 'warning'
            })
            return
          }
          this.$api.general.editpwd({
            username: this.forgetForm.account,
            password: this.$utils.encrypt(this.forgetForm.newPassword, 'zysofthnzx202002'),
            verifyCode: this.forgetForm.smsValidation
          })
        }
      })
    },
    settime () {
      if (this.countdown === 0) {
        this.smsText = '获取验证码'
        this.countdown = 60
        return
      } else {
        this.smsText = '重新发送' + this.countdown + 'S'
        this.countdown--
      }
      setTimeout(() => {
        this.settime()
      }, 1000)
    }
  }
}
</script>
<style lang="scss" scpoed>
// @import './login.scss';
.forgetText {
  font-size: $textSize12;
  line-height: 20px;
}

.login-form-box-pass {
  padding: 24px;
  min-height: 326px;
}

.login-form {
  padding-top: 12px;

  .login-form-input {
    margin-bottom: 19px;

    .el-form-item__content {
      line-height: 36px;

      .el-input__inner {
        height: 36px;
        line-height: 36px;
      }
    }
  }

  .form-valid {
    width: 100%;
    height: 36px;
    margin-bottom: 19px;

    .el-form-item__content {
      display: flex;
    }

    .el-input {
      width: 154px;
      height: 36px;

      input {
        height: 36px;
        line-height: 36px;
      }
    }

    .ValidCode {
      background-color: #ccc;
      margin-left: 20px;
    }

    .el-button {
      margin-left: 20px;
      flex: 1;
    }
  }

  .login-form-operation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;

    .login-form-operation-box {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .login-form-operation-text {
        font-size: $textSize12;
        line-height: 14px;
        font-family: SimSun;
        color: $zy-color;
        font-weight: 400;
        cursor: pointer;
      }

      .login-form-operation-l {
        width: 1px;
        height: 14px;
        margin: 0 9px;
        background-color: #e4dee0;
      }
    }
  }

  .login-form-button {
    width: 100%;
    background-color: $zy-color;
    padding: 0;
    height: 38px;
    line-height: 38px;
  }
}

.form-valid {
  width: 100%;
  height: 36px;
  margin-bottom: 19px;

  .el-form-item__content {
    display: flex;
  }

  .el-input {
    width: 154px;
    height: 36px;

    input {
      height: 36px;
      line-height: 36px;
    }
  }

  .ValidCode {
    background-color: #ccc;
    margin-left: 20px;
  }

  .el-button {
    margin-left: 20px;
    flex: 1;
  }
}
</style>
