{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\forgetPassword\\forgetPassword.vue?vue&type=style&index=0&id=82edf39a&lang=scss&scpoed=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\forgetPassword\\forgetPassword.vue", "mtime": 1755833016358}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["forgetPassword.vue"], "names": [], "mappings": ";AA2NA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "forgetPassword.vue", "sourceRoot": "src/views/main/forgetPassword", "sourcesContent": ["<template>\r\n  <div class=\"login-form-box-pass\">\r\n    <div class=\"forgetText\">密码至少在8位以上，大小写字母、数字、符号至少包含3种。</div>\r\n    <el-form :model=\"forgetForm\" :rules=\"forgetrules\" class=\"login-form\" ref=\"forgetForm\" @submit.native.prevent>\r\n      <el-form-item prop=\"account\" class=\"login-form-input\">\r\n        <el-input v-model=\"forgetForm.account\" placeholder=\"账号/手机号/证件号码\" clearable></el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item class=\"login-form-input form-valid\">\r\n        <el-input v-model=\"forgetForm.validCode\" placeholder=\"验证码\" clearable> </el-input>\r\n        <valid :length=\"4\" :heigth=\"'36px'\" :value.sync=\"validCode\"></valid>\r\n      </el-form-item>\r\n      <!-- <div class=\"form-valid\">\r\n        <el-input\r\n          v-model=\"forgetForm.validCode\"\r\n          placeholder=\"验证码\"\r\n          clearable\r\n        > </el-input>\r\n        <valid\r\n          :length=\"4\"\r\n          :heigth=\"'36px'\"\r\n          :value.sync=\"validCode\"\r\n        ></valid>\r\n      </div> -->\r\n      <el-form-item prop=\"newPassword\" class=\"login-form-input\">\r\n\r\n        <el-input type=\"password\" v-model=\"forgetForm.newPassword\" placeholder=\"请输入新密码\" clearable></el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"newPassword\" class=\"login-form-input\">\r\n\r\n        <el-input type=\"password\" v-model=\"forgetForm.newPasswordAgian\" placeholder=\"请再次输入新密码\" clearable></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"login-form-input form-valid\">\r\n        <el-input v-model=\"forgetForm.smsValidation\" placeholder=\"短信验证码\" clearable> </el-input>\r\n        <el-button type=\"primary\" @click=\"validCodetesting\" :disabled=\"smsText != '获取验证码'\"> {{ smsText }} </el-button>\r\n      </el-form-item>\r\n      <div class=\"login-form-operation\">\r\n        <div class=\"login-form-operation-box\">\r\n          <div class=\"login-form-operation-text\" @click=\"gologin\">返回登录</div>\r\n        </div>\r\n      </div>\r\n      <el-button type=\"primary\" class=\"login-form-button\" @click=\"editpwd('forgetForm')\"> 确定 </el-button>\r\n    </el-form>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport valid from '@/components/valid/valid'\r\nexport default {\r\n  components: { valid },\r\n  data () {\r\n    return {\r\n      validCode: '',\r\n      forgetForm: {\r\n        account: '',\r\n        validCode: '',\r\n        smsValidation: '',\r\n        newPassword: '',\r\n        newPasswordAgian: ''\r\n      },\r\n      forgetrules: {\r\n        account: [\r\n          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        newPasswordAgian: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        smsValidation:\r\n          [{ required: true, message: '请输入短信验证码', trigger: 'blur' }]\r\n      },\r\n      smsShow: false,\r\n      smsText: '获取验证码',\r\n      countdown: 0\r\n    }\r\n  },\r\n  methods: {\r\n    // 密码验证函数：8位以上，大小写字母、数字、符号至少包含3种\r\n    validatePassword (password) {\r\n      if (password.length < 8) {\r\n        return false\r\n      }\r\n\r\n      let typeCount = 0\r\n\r\n      // 检查是否包含小写字母\r\n      if (/[a-z]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含大写字母\r\n      if (/[A-Z]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含数字\r\n      if (/[0-9]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含符号\r\n      if (/[~!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      return typeCount >= 3\r\n    },\r\n\r\n    forget () {\r\n      // this.$router.push({ name: 'changepassword' })\r\n      this.forgetflag = true\r\n      this.forgetForm.account = this.loginForm.account\r\n      this.loginForm.smsValidation = ''\r\n      this.countdown = 0\r\n    },\r\n    gologin () {\r\n      // this.countdown = 0\r\n      // this.forgetflag = false\r\n      this.$emit('gologin')\r\n    },\r\n    validCodetesting () {\r\n      if (this.forgetForm.account === '') {\r\n        this.$message({\r\n          message: '请先输入账号/手机号/证件号码！',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {\r\n        this.forgetForm.validCode = ''\r\n        this.$message.warning('验证码不正确!')\r\n        return\r\n      }\r\n      if (!this.validatePassword(this.forgetForm.newPassword)) {\r\n        this.$message({\r\n          message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {\r\n        this.$message({\r\n          message: '两次密码输入不一致！',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$api.general.sendmessage({\r\n        account: this.forgetForm.account,\r\n        password: ''\r\n      }).then(res => {\r\n        this.countdown = 60\r\n        this.settime()\r\n        this.$message({\r\n          message: '短信发送成功!',\r\n          type: 'success'\r\n        })\r\n      })\r\n    },\r\n    editpwd (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {\r\n            this.forgetForm.validCode = ''\r\n            this.$message.warning('验证码不正确!')\r\n            return\r\n          }\r\n          if (!this.validatePassword(this.forgetForm.newPassword)) {\r\n            this.$message({\r\n              message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {\r\n            this.$message({\r\n              message: '两次密码输入不一致！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n          if (this.forgetForm.smsValidation === '') {\r\n            this.$message({\r\n              message: '请输入验证码！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n          this.$api.general.editpwd({\r\n            username: this.forgetForm.account,\r\n            password: this.$utils.encrypt(this.forgetForm.newPassword, 'zysofthnzx202002'),\r\n            verifyCode: this.forgetForm.smsValidation\r\n          })\r\n        }\r\n      })\r\n    },\r\n    settime () {\r\n      if (this.countdown === 0) {\r\n        this.smsText = '获取验证码'\r\n        this.countdown = 60\r\n        return\r\n      } else {\r\n        this.smsText = '重新发送' + this.countdown + 'S'\r\n        this.countdown--\r\n      }\r\n      setTimeout(() => {\r\n        this.settime()\r\n      }, 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scpoed>\r\n// @import './login.scss';\r\n.forgetText {\r\n  font-size: $textSize12;\r\n  line-height: 20px;\r\n}\r\n\r\n.login-form-box-pass {\r\n  padding: 24px;\r\n  min-height: 326px;\r\n}\r\n\r\n.login-form {\r\n  padding-top: 12px;\r\n\r\n  .login-form-input {\r\n    margin-bottom: 19px;\r\n\r\n    .el-form-item__content {\r\n      line-height: 36px;\r\n\r\n      .el-input__inner {\r\n        height: 36px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-valid {\r\n    width: 100%;\r\n    height: 36px;\r\n    margin-bottom: 19px;\r\n\r\n    .el-form-item__content {\r\n      display: flex;\r\n    }\r\n\r\n    .el-input {\r\n      width: 154px;\r\n      height: 36px;\r\n\r\n      input {\r\n        height: 36px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n\r\n    .ValidCode {\r\n      background-color: #ccc;\r\n      margin-left: 20px;\r\n    }\r\n\r\n    .el-button {\r\n      margin-left: 20px;\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .login-form-operation {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 24px;\r\n\r\n    .login-form-operation-box {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n\r\n      .login-form-operation-text {\r\n        font-size: $textSize12;\r\n        line-height: 14px;\r\n        font-family: SimSun;\r\n        color: $zy-color;\r\n        font-weight: 400;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .login-form-operation-l {\r\n        width: 1px;\r\n        height: 14px;\r\n        margin: 0 9px;\r\n        background-color: #e4dee0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .login-form-button {\r\n    width: 100%;\r\n    background-color: $zy-color;\r\n    padding: 0;\r\n    height: 38px;\r\n    line-height: 38px;\r\n  }\r\n}\r\n\r\n.form-valid {\r\n  width: 100%;\r\n  height: 36px;\r\n  margin-bottom: 19px;\r\n\r\n  .el-form-item__content {\r\n    display: flex;\r\n  }\r\n\r\n  .el-input {\r\n    width: 154px;\r\n    height: 36px;\r\n\r\n    input {\r\n      height: 36px;\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .ValidCode {\r\n    background-color: #ccc;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .el-button {\r\n    margin-left: 20px;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}