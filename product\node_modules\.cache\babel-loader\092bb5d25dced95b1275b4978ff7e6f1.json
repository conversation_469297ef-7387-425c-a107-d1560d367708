{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\BarScrollChart.vue", "mtime": 1755766342420}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAKA;AAEA;EACAA,sBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAC;IAFA,CALA;IASAE;MACAH,WADA;MAEAC,cAFA;MAGAG;IAHA,CATA;IAcAC;MACAL,aADA;MAEAI;IAFA;EAdA,CAFA;;EAqBAE;IACA;MACAC,WADA;MAEAC,WAFA;MAGAC,eAHA,CAIA;;IAJA;EAMA,CA5BA;;EA6BAC;IACA;IACA;EACA,CAhCA;;EAiCAC;IACA;IACA;EACA,CApCA;;EAqCAC;IACAC;MACA;QACA;QACA;UACA;UACA;YACAb,cADA;YAEAc,IAFA;YAGAC,IAHA;YAIAC,KAJA;YAKAC,KALA;YAMAC,aACA;cAAAC;cAAAC;YAAA,CADA,EAEA;cAAAD;cAAAC;YAAA,CAFA;UANA;QAWA,CAbA,MAaA;UACA;UACA;YACApB,cADA;YAEAc,IAFA;YAGAC,IAHA;YAIAC,KAJA;YAKAC,KALA;YAMAC,aACA;cAAAC;cAAAC;YAAA,CADA,EAEA;cAAAD;cAAAC;YAAA,CAFA;UANA;QAWA;MACA,CA7BA,MA6BA;QACA;QACA;UACApB,cADA;UAEAc,IAFA;UAGAC,IAHA;UAIAC,KAJA;UAKAC,KALA;UAMAC,aACA;YAAAC;YAAAC;UAAA,CADA,EAEA;YAAAD;YAAAC;UAAA,CAFA;QANA;MAWA;IACA,CA7CA;;IA8CAC;MACA;IACA,CAhDA;;IAiDAC;MACA;QACAC;UACAC,eADA;UAEAC;YACAzB;UADA,CAFA;UAKA0B,uCALA;UAMAC,sCANA;UAOAC,cAPA;UAQAC;YACAT,gBADA;YAEAU;UAFA,CARA;UAYAC;YACA;cACA;cACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eATA;YAUA;;YACA;UACA;QA3BA,CADA;QA8BAC;UACAC,yFADA;UAEAC,SAFA;UAGAC,OAHA;UAIAC,UAJA;UAKAC;QALA,CA9BA;QAqCAC;UACAtC,aADA;UAEAuC,MAFA;UAGAC,wDAHA;UAIAC;YAAAC;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAE;YAAAF;UAAA,CANA;UAOAG;YAAAH;UAAA;QAPA,CArCA;QA8CAI,QACA;UACA9C,gBADA;UAEA+C,aAFA;UAGAzC,iCAHA;UAIAsC;YAAAF;UAAA,CAJA;UAKAC;YAAAD;UAAA,CALA;UAMAG;YACAH,UADA;YAEAM,cAFA;YAGAC,UAHA;YAIAlB;cACA;gBACA;cACA,CAFA,MAEA;gBACA;gBACA;cACA;YACA,CAXA;YAYAmB;cACAC;gBACA/B,iCADA;gBAEAU,YAFA;gBAGAsB,iBAHA;gBAIAC,iBAJA;gBAKAL,aALA;gBAMA;gBACAM;cAPA,CADA;cAUAzD;gBACAuB,aADA;gBAEAU,YAFA;gBAGAwB;cAHA;YAVA;UAZA;QANA,CADA,EAqCA;UACA;UACAtD,gBAFA;UAGA+C,aAHA;UAIAzC,kCAJA;UAKAsC;YAAAF;UAAA,CALA;UAMAC;YAAAD;UAAA,CANA;UAOAG;YACAH,UADA;YAEAtB,gBAFA;YAGAU,YAHA;YAIAkB,aAJA;YAKAC;UALA;QAPA,CArCA,CA9CA;QAmGAM,SACA;UACAvD,WADA;UAEAwD,WAFA;UAGAC,aAHA;UAIAnD;YACAoD,iBADA;YAEA7D,eAFA;YAGA8D;cACAvC,6EADA;cAEAwC;YAFA;UAHA,GAJA;UAYAC;QAZA,CADA,EAeA;UACA;UACA7D,WAFA;UAGAwD,WAHA;UAIAC,aAJA;UAKAnD,yEALA;UAMAqD;YACAvC,2BADA;YAEAwC;UAFA,CANA;UAUAE,eAVA;UAWAD;QAXA,CAfA;MAnGA;IAiIA,CAnLA;;IAoLAE;MACA;MACA;MACA;MACA;MACAC;IACA,CA1LA;;IA2LAC;MACA;MACA;;MACA;QACA3D;MACA,CAFA,MAEA;QACA;QACA;QACA;;QACA;UACAA;QACA,CAFA,MAEA;UACAA;QACA;MACA,CAdA,CAeA;;;MACA;IACA,CA5MA;;IA6MA4D;MACA;MACA;QACA;QACA;QACA;MACA,CAJA,EAIA,IAJA;IAKA,CApNA;;IAqNAC;MACA;IACA;;EAvNA;AArCA", "names": ["name", "props", "id", "type", "required", "showCount", "chartData", "default", "alternateColors", "data", "chart", "timer", "currentIndex", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "getBarColor", "x", "y", "x2", "y2", "colorStops", "offset", "color", "getBgBarColor", "getOption", "tooltip", "trigger", "axisPointer", "backgroundColor", "borderColor", "borderWidth", "textStyle", "fontSize", "formatter", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "min", "max", "splitLine", "show", "axisLine", "axisTick", "axisLabel", "yAxis", "inverse", "align", "margin", "rich", "num", "fontFamily", "fontWeight", "padding", "series", "<PERSON><PERSON><PERSON><PERSON>", "yAxisIndex", "value", "itemStyle", "borderRadius", "z", "barGap", "initChart", "window", "<PERSON><PERSON><PERSON>", "startScroll", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["BarScrollChart.vue"], "sourcesContent": ["<template>\r\n  <div :id=\"id\" class=\"bar-scroll-chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      required: true\r\n    },\r\n    showCount: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      required: true,\r\n      default: () => []\r\n    },\r\n    alternateColors: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      chart: null,\r\n      timer: null,\r\n      currentIndex: 0\r\n      // showCount: 5 // 一屏显示5条\r\n    }\r\n  },\r\n  mounted () {\r\n    this.initChart()\r\n    this.startScroll()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.chart) this.chart.dispose()\r\n    if (this.timer) clearInterval(this.timer)\r\n  },\r\n  methods: {\r\n    getBarColor (index = 0) {\r\n      if (this.alternateColors) {\r\n        // 交替颜色：蓝色和黄色\r\n        if (index % 2 === 0) {\r\n          // 蓝色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#062553' },\r\n              { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n            ]\r\n          }\r\n        } else {\r\n          // 黄色渐变\r\n          return {\r\n            type: 'linear',\r\n            x: 0,\r\n            y: 0,\r\n            x2: 1,\r\n            y2: 0,\r\n            colorStops: [\r\n              { offset: 0, color: '#072756' },\r\n              { offset: 1, color: 'rgba(245, 231, 79, 1)' }\r\n            ]\r\n          }\r\n        }\r\n      } else {\r\n        // 默认蓝色渐变\r\n        return {\r\n          type: 'linear',\r\n          x: 0,\r\n          y: 0,\r\n          x2: 1,\r\n          y2: 0,\r\n          colorStops: [\r\n            { offset: 0, color: '#062553' },\r\n            { offset: 1, color: 'rgba(31, 198, 255, 1)' }\r\n          ]\r\n        }\r\n      }\r\n    },\r\n    getBgBarColor () {\r\n      return 'rgba(35,225,255,0.08)'\r\n    },\r\n    getOption (data) {\r\n      return {\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'none'\r\n          },\r\n          backgroundColor: 'rgba(0, 20, 40, 0.9)',\r\n          borderColor: 'rgba(31, 198, 255, 0.8)',\r\n          borderWidth: 1,\r\n          textStyle: {\r\n            color: '#FFFFFF',\r\n            fontSize: 14\r\n          },\r\n          formatter: (params) => {\r\n            if (params && params.length > 0) {\r\n              const data = params[0]\r\n              return `\r\n                <div style=\"padding: 8px;\">\r\n                  <div style=\"color: #1FC6FF; font-weight: bold; margin-bottom: 4px;\">\r\n                    ${data.name}\r\n                  </div>\r\n                  <div style=\"color: #FFFFFF;\">\r\n                    数量: <span style=\"color: #F5E74F; font-weight: bold;\">${data.value}</span>\r\n                  </div>\r\n                </div>\r\n              `\r\n            }\r\n            return ''\r\n          }\r\n        },\r\n        grid: {\r\n          left: this.id === 'committee-statistics' ? 15 : this.id === 'activityTypeChart' ? 45 : 35,\r\n          right: 10,\r\n          top: 15,\r\n          bottom: 10,\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: 'value',\r\n          min: 0,\r\n          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,\r\n          splitLine: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          axisLabel: { show: false }\r\n        },\r\n        yAxis: [\r\n          {\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.name),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              align: 'right',\r\n              margin: 16,\r\n              formatter: (value, idx) => {\r\n                if (this.id === 'activityTypeChart') {\r\n                  return `{name|${value}}`\r\n                } else {\r\n                  const num = ((this.currentIndex + idx) % this.chartData.length) + 1\r\n                  return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`\r\n                }\r\n              },\r\n              rich: {\r\n                num: {\r\n                  color: 'rgba(255, 255, 255, 0.5)',\r\n                  fontSize: 13,\r\n                  fontFamily: 'DIN',\r\n                  fontWeight: '500',\r\n                  align: 'left',\r\n                  // width: 25,\r\n                  padding: [4, 0, 0, 0]\r\n                },\r\n                name: {\r\n                  color: '#fff',\r\n                  fontSize: 15,\r\n                  padding: [0, 0, 0, 4]\r\n                }\r\n              }\r\n            }\r\n          },\r\n          {\r\n            // 右侧数值\r\n            type: 'category',\r\n            inverse: true,\r\n            data: data.map(item => item.value),\r\n            axisTick: { show: false },\r\n            axisLine: { show: false },\r\n            axisLabel: {\r\n              show: true,\r\n              color: '#A0F6FF',\r\n              fontSize: 18,\r\n              align: 'left',\r\n              margin: 12\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            type: 'bar',\r\n            barWidth: 6,\r\n            yAxisIndex: 0,\r\n            data: data.map((item, index) => ({\r\n              value: item.value,\r\n              name: item.name,\r\n              itemStyle: {\r\n                color: this.getBarColor(this.alternateColors ? (this.currentIndex + index) : 0),\r\n                borderRadius: 6\r\n              }\r\n            })),\r\n            z: 2\r\n          },\r\n          {\r\n            // 背景条\r\n            type: 'bar',\r\n            barWidth: 8,\r\n            yAxisIndex: 0,\r\n            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),\r\n            itemStyle: {\r\n              color: this.getBgBarColor(),\r\n              borderRadius: 6\r\n            },\r\n            barGap: '-100%',\r\n            z: 1\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    initChart () {\r\n      const chartContainer = document.getElementById(this.id)\r\n      if (!chartContainer) return\r\n      this.chart = echarts.init(chartContainer)\r\n      this.renderChart()\r\n      window.addEventListener('resize', this.resizeChart)\r\n    },\r\n    renderChart () {\r\n      // 滚动窗口数据\r\n      let data = []\r\n      if (this.chartData.length <= this.showCount) {\r\n        data = this.chartData\r\n      } else {\r\n        // 实现无缝滚动\r\n        const start = this.currentIndex\r\n        const end = start + this.showCount\r\n        if (end <= this.chartData.length) {\r\n          data = this.chartData.slice(start, end)\r\n        } else {\r\n          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))\r\n        }\r\n      }\r\n      // 保持原顺序，新数据在最下方\r\n      this.chart.setOption(this.getOption(data), true)\r\n    },\r\n    startScroll () {\r\n      if (this.timer) clearInterval(this.timer)\r\n      this.timer = setInterval(() => {\r\n        if (this.chartData.length <= this.showCount) return\r\n        this.currentIndex = (this.currentIndex + 1) % this.chartData.length\r\n        this.renderChart()\r\n      }, 3000)\r\n    },\r\n    resizeChart () {\r\n      if (this.chart) this.chart.resize()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bar-scroll-chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}