{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\system-settings\\user-management\\change-password\\change-password.vue?vue&type=style&index=0&id=57ece1df&lang=scss&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\system-settings\\user-management\\change-password\\change-password.vue", "mtime": 1755832904407}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouY2hhbmdlLXBhc3N3b3JkIHsNCiAgd2lkdGg6IDY5MnB4Ow0KICBoZWlnaHQ6IDEwMCU7DQogIHBhZGRpbmc6IDI0cHggNDBweDsNCn0NCg=="}, {"version": 3, "sources": ["change-password.vue"], "names": [], "mappings": ";AA2HA;AACA;AACA;AACA;AACA", "file": "change-password.vue", "sourceRoot": "src/views/system-settings/user-management/change-password", "sourcesContent": ["<template>\r\n  <div class=\"change-password\">\r\n    <el-form :model=\"form\" :rules=\"rules\" inline ref=\"form\" label-position=\"top\" class=\"newForm\">\r\n      <el-form-item label=\"新密码（8位以上，大小写字母、数字、符号至少包含3种）\" class=\"form-input\" prop=\"pass\">\r\n        <el-input placeholder=\"请输入密码\" type=\"password\" v-model=\"form.pass\" clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码\" class=\"form-input\" prop=\"checkPass\">\r\n        <el-input placeholder=\"请输入密码\" type=\"password\" v-model=\"form.checkPass\" clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\" @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'changePassword',\r\n  data () {\r\n    var validatePass = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请输入密码'))\r\n      } else {\r\n        // 密码验证：8位以上，大小写字母、数字、符号至少包含3种\r\n        if (value.length < 8) {\r\n          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))\r\n          return\r\n        }\r\n\r\n        let typeCount = 0\r\n\r\n        // 检查是否包含小写字母\r\n        if (/[a-z]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含大写字母\r\n        if (/[A-Z]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含数字\r\n        if (/[0-9]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含符号\r\n        if (/[~!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        if (typeCount >= 3) {\r\n          if (this.form.checkPass !== '') {\r\n            this.$refs.form.validateField('checkPass')\r\n          }\r\n          callback()\r\n        } else {\r\n          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))\r\n        }\r\n      }\r\n    }\r\n    var validatePass2 = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请再次输入密码'))\r\n      } else if (value !== this.form.pass) {\r\n        callback(new Error('两次输入密码不一致!'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      form: {\r\n        pass: '',\r\n        checkPass: ''\r\n      },\r\n      rules: {\r\n        pass: [\r\n          { required: true, validator: validatePass, trigger: 'blur' }\r\n        ],\r\n        checkPass: [\r\n          { required: true, validator: validatePass2, trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  props: ['id'],\r\n  methods: {\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/wholeuser/editpwd'\r\n          this.$api.systemSettings.generalAdd(url, {\r\n            userId: this.id,\r\n            password: this.$utils.encrypt(this.form.checkPass, 'zysofthnzx202002')\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('Callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('Callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.change-password {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n}\r\n</style>\r\n"]}]}