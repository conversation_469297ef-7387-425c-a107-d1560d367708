{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\system-settings\\user-management\\change-password\\change-password.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\system-settings\\user-management\\change-password\\change-password.vue", "mtime": 1755832904407}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["change-password.vue"], "names": [], "mappings": ";AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "change-password.vue", "sourceRoot": "src/views/system-settings/user-management/change-password", "sourcesContent": ["<template>\r\n  <div class=\"change-password\">\r\n    <el-form :model=\"form\" :rules=\"rules\" inline ref=\"form\" label-position=\"top\" class=\"newForm\">\r\n      <el-form-item label=\"新密码（8位以上，大小写字母、数字、符号至少包含3种）\" class=\"form-input\" prop=\"pass\">\r\n        <el-input placeholder=\"请输入密码\" type=\"password\" v-model=\"form.pass\" clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码\" class=\"form-input\" prop=\"checkPass\">\r\n        <el-input placeholder=\"请输入密码\" type=\"password\" v-model=\"form.checkPass\" clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\" @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'changePassword',\r\n  data () {\r\n    var validatePass = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请输入密码'))\r\n      } else {\r\n        // 密码验证：8位以上，大小写字母、数字、符号至少包含3种\r\n        if (value.length < 8) {\r\n          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))\r\n          return\r\n        }\r\n\r\n        let typeCount = 0\r\n\r\n        // 检查是否包含小写字母\r\n        if (/[a-z]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含大写字母\r\n        if (/[A-Z]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含数字\r\n        if (/[0-9]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含符号\r\n        if (/[~!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        if (typeCount >= 3) {\r\n          if (this.form.checkPass !== '') {\r\n            this.$refs.form.validateField('checkPass')\r\n          }\r\n          callback()\r\n        } else {\r\n          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))\r\n        }\r\n      }\r\n    }\r\n    var validatePass2 = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请再次输入密码'))\r\n      } else if (value !== this.form.pass) {\r\n        callback(new Error('两次输入密码不一致!'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      form: {\r\n        pass: '',\r\n        checkPass: ''\r\n      },\r\n      rules: {\r\n        pass: [\r\n          { required: true, validator: validatePass, trigger: 'blur' }\r\n        ],\r\n        checkPass: [\r\n          { required: true, validator: validatePass2, trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  props: ['id'],\r\n  methods: {\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/wholeuser/editpwd'\r\n          this.$api.systemSettings.generalAdd(url, {\r\n            userId: this.id,\r\n            password: this.$utils.encrypt(this.form.checkPass, 'zysofthnzx202002')\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('Callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('Callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.change-password {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n}\r\n</style>\r\n"]}]}