{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\system-settings\\user-management\\change-password\\change-password.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\system-settings\\user-management\\change-password\\change-password.vue", "mtime": 1755832904407}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";AAmBA;EACAA,sBADA;;EAEAC;IACA;MACA;QACAC;MACA,CAFA,MAEA;QACA;QACA;UACAA;UACA;QACA;;QAEA,kBAPA,CASA;;QACA;UACAC;QACA,CAZA,CAcA;;;QACA;UACAA;QACA,CAjBA,CAmBA;;;QACA;UACAA;QACA,CAtBA,CAwBA;;;QACA;UACAA;QACA;;QAEA;UACA;YACA;UACA;;UACAD;QACA,CALA,MAKA;UACAA;QACA;MACA;IACA,CAzCA;;IA0CA;MACA;QACAA;MACA,CAFA,MAEA;QACAA;MACA,CAFA,MAEA;QACAA;MACA;IACA,CARA;;IASA;MACAE;QACAC,QADA;QAEAC;MAFA,CADA;MAKAC;QACAF,OACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAJ,YACA;UAAAE;UAAAC;UAAAC;QAAA,CADA;MAJA;IALA;EAcA,CApEA;;EAqEAC,aArEA;EAsEAC;IACAC;MACA;QACA;UACA;UACA;YACAC,eADA;YAEAC;UAFA,GAGAC,IAHA,CAGAC;YACA;cAAAC;cAAAC;YAAA;;YACA;cACA;gBACAC,eADA;gBAEAC;cAFA;cAIA;YACA;UACA,CAZA;QAaA,CAfA,MAeA;UACA;YACAD,iBADA;YAEAC;UAFA;UAIA;QACA;MACA,CAvBA;IAwBA,CA1BA;;IA2BAC;MACA;IACA;;EA7BA;AAtEA", "names": ["name", "data", "callback", "typeCount", "form", "pass", "checkPass", "rules", "required", "validator", "trigger", "props", "methods", "submitForm", "userId", "password", "then", "res", "<PERSON><PERSON><PERSON>", "errmsg", "message", "type", "resetForm"], "sourceRoot": "src/views/system-settings/user-management/change-password", "sources": ["change-password.vue"], "sourcesContent": ["<template>\r\n  <div class=\"change-password\">\r\n    <el-form :model=\"form\" :rules=\"rules\" inline ref=\"form\" label-position=\"top\" class=\"newForm\">\r\n      <el-form-item label=\"新密码（8位以上，大小写字母、数字、符号至少包含3种）\" class=\"form-input\" prop=\"pass\">\r\n        <el-input placeholder=\"请输入密码\" type=\"password\" v-model=\"form.pass\" clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码\" class=\"form-input\" prop=\"checkPass\">\r\n        <el-input placeholder=\"请输入密码\" type=\"password\" v-model=\"form.checkPass\" clearable>\r\n        </el-input>\r\n      </el-form-item>\r\n      <div class=\"form-button\">\r\n        <el-button type=\"primary\" @click=\"submitForm('form')\">确定</el-button>\r\n        <el-button @click=\"resetForm('form')\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'changePassword',\r\n  data () {\r\n    var validatePass = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请输入密码'))\r\n      } else {\r\n        // 密码验证：8位以上，大小写字母、数字、符号至少包含3种\r\n        if (value.length < 8) {\r\n          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))\r\n          return\r\n        }\r\n\r\n        let typeCount = 0\r\n\r\n        // 检查是否包含小写字母\r\n        if (/[a-z]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含大写字母\r\n        if (/[A-Z]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含数字\r\n        if (/[0-9]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        // 检查是否包含符号\r\n        if (/[~!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]/.test(value)) {\r\n          typeCount++\r\n        }\r\n\r\n        if (typeCount >= 3) {\r\n          if (this.form.checkPass !== '') {\r\n            this.$refs.form.validateField('checkPass')\r\n          }\r\n          callback()\r\n        } else {\r\n          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))\r\n        }\r\n      }\r\n    }\r\n    var validatePass2 = (rule, value, callback) => {\r\n      if (value === '') {\r\n        callback(new Error('请再次输入密码'))\r\n      } else if (value !== this.form.pass) {\r\n        callback(new Error('两次输入密码不一致!'))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n    return {\r\n      form: {\r\n        pass: '',\r\n        checkPass: ''\r\n      },\r\n      rules: {\r\n        pass: [\r\n          { required: true, validator: validatePass, trigger: 'blur' }\r\n        ],\r\n        checkPass: [\r\n          { required: true, validator: validatePass2, trigger: 'blur' }\r\n        ]\r\n      }\r\n    }\r\n  },\r\n  props: ['id'],\r\n  methods: {\r\n    submitForm (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          var url = '/wholeuser/editpwd'\r\n          this.$api.systemSettings.generalAdd(url, {\r\n            userId: this.id,\r\n            password: this.$utils.encrypt(this.form.checkPass, 'zysofthnzx202002')\r\n          }).then(res => {\r\n            var { errcode, errmsg } = res\r\n            if (errcode === 200) {\r\n              this.$message({\r\n                message: errmsg,\r\n                type: 'success'\r\n              })\r\n              this.$emit('Callback')\r\n            }\r\n          })\r\n        } else {\r\n          this.$message({\r\n            message: '请输入必填项',\r\n            type: 'warning'\r\n          })\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    resetForm (formName) {\r\n      this.$emit('Callback')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.change-password {\r\n  width: 692px;\r\n  height: 100%;\r\n  padding: 24px 40px;\r\n}\r\n</style>\r\n"]}]}