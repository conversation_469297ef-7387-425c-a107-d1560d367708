{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\login-qdzx\\login-qdzx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\login-qdzx\\login-qdzx.vue", "mtime": 1755832720145}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoIA;AACA;AACA;AACA;AACA;AACA;AACA;EACAA,aADA;;EAEAC;IACA;MACAC,gBADA;MAEAC,aAFA;MAGAC,iBAHA;MAIAC,iBAJA;MAKAC,cALA;MAMAC,YANA;MAOAC,eAPA;MAQAC;QACAC,WADA;QAEAC,YAFA;QAGAC;MAHA,CARA;MAaAC;QACAH,UACA;UAAAI;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAL,WACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAJ;MAPA,CAbA;MAsBAK,cAtBA;MAuBAC,QAvBA;MAwBAC,QAxBA;MAyBAC,WAzBA;MA0BAC,gBA1BA;MA2BAC,gBA3BA;MA4BAC,eA5BA;MA6BAC,gBA7BA;MA8BAC;IA9BA;EAgCA,CAnCA;;EAoCAC;IACAC,UADA;IAEAC,cAFA;IAGAC,KAHA;IAIAC,WAJA;IAKAC;EALA,CApCA;;EA2CAC;IACA;MACA;IACA,CAFA,MAEA;MACA;IACA;;IACA;IACA;;IACA;MACA;QACA;QACA;QACA;MACA;IACA;;IACA;EACA,CA3DA;;EA4DAC;IACA9B;MACA;QACA;MACA,CAFA,MAEA;QACA;QACA+B;MACA;IACA;;EARA,CA5DA;EAsEAC;IACAC;MACA;MACAC;IACA,CAJA;;IAKA;MACA;MACA;QAAApC;MAAA;MACA;IACA,CATA;;IAUA;MACA;QACAqC;;QACA;UACA;YACA5B,+BADA;YAEAC;UAFA;UAIA;YAAA4B;UAAA;;UACA;YACA;YACAC;cACA;YACA,CAFA,EAEA,IAFA;YAGA;cACAzB,wBADA;cAEA0B;YAFA;UAIA;QACA,CAhBA,MAgBA;UACA;YACA;cACA/B,+BADA;cAEAC;YAFA;YAIA;cAAA+B;YAAA;;YACA;cACA;cACAF;gBACA;cACA,CAFA,EAEA,IAFA;cAGA;gBACAzB,wBADA;gBAEA0B;cAFA;YAIA;UACA,CAhBA,MAgBA;YACA;UACA;QACA;MACA,CAvCA;IAwCA,CAnDA;;IAoDA;MACA;MACA;QAAAxC;MAAA;MACA;IACA,CAxDA;;IAyDA0C;MACA;IACA,CA3DA;;IA4DAC;MACA;MACA;MACA;IACA,CAhEA;;IAiEAC;MACA;MACA;IACA,CApEA;;IAqEAC;MACA;MACA;IACA,CAxEA;;IAyEAC;MACA;QACA;MACA;;MACA;;MACA;QACA;MACA,CAFA,MAEA;QACA;QACAC;QACAd;MACA;IACA,CArFA;;IAsFA;MACA;QACAe;MADA;MAGAX;MACA;QAAArC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;MACA,CAFA,EAEA,MAFA;IAGA,CAnGA;;IAoGA;MACA;QACAiD;MADA;;MAGA;QACA5B;UACA;QACA,CAFA,EAEA,IAFA;MAGA;;MACA;QACA;QACAY;QACA;QACA;UAAAjC;UAAAkD;QAAA;QACA;UACApC,gBADA;UAEA0B;QAFA;QAIAO;QACAA;;QACA;UACA;YACA;UACA,CAFA,MAEA;YACA;cAAAI;YAAA;UACA;QACA,CANA,MAMA;UACA;YACAD;cACA;gBACAb;gBACA;cACA;YACA,CALA;UAMA,CAPA,MAOA;YACA;UACA;QACA;MACA;IACA,CA3IA;;IA4IAe;MACA;IACA,CA9IA;;IA+IAC;MACA;QAAAxC;QAAAC;QAAAC;MAAA;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UACA;YACA;YACA;YACA;UACA;;UACA;YACAuC,gCADA;YAEA5C,0EAFA;YAGA6C;UAHA,GAIAC,IAJA,CAIAC;YACA;cAAAzD;cAAAkD;YAAA;YACA;cACApC,gBADA;cAEA0B;YAFA;YAIAO;YACAA;;YACA;cACA;gBACA;cACA,CAFA,MAEA;gBACA;kBAAAI;gBAAA;cACA;YACA,CANA,MAMA;cACA;gBACAD;kBACA;oBACAb;oBACA;kBACA;gBACA,CALA;cAMA,CAPA,MAOA;gBACA;cACA;YACA;;YACA;cAAAhC;cAAAI;cAAAC;YAAA;YACAgD;UACA,CAhCA,EAgCAC,KAhCA,CAgCA;YACA;YACA;UACA,CAnCA;QAoCA,CAtDA,MAsDA;UACA;QACA;MACA,CA1DA;IA2DA,CA5MA;;IA6MAC;MACAxB;IACA;;EA/MA;AAtEA", "names": ["name", "data", "loginImgList", "loginTab", "forgetflag", "switchLogin", "checked", "valid", "disabled", "loginForm", "account", "password", "verify", "rules", "required", "message", "trigger", "loading", "text", "uuid", "time", "qrloading", "setTimeout", "isShowIE", "showVrify", "setTimeoutNum", "components", "xylSliding", "xylSlidingItem", "vueQr", "slideVerify", "forgetpassword", "created", "watch", "clearTimeout", "methods", "btnback", "window", "console", "<PERSON><PERSON><PERSON>", "setInterval", "type", "errcodes", "gologin", "onSuccess", "onAgain", "handleClick", "switchMethods", "sessionStorage", "codes", "qrCodeId", "projects", "path", "success", "submitForm", "username", "verifyCode", "then", "res", "localStorage", "catch", "browser"], "sourceRoot": "src/views/main/login-qdzx", "sources": ["login-qdzx.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-qdzx\">\r\n    <el-carousel class=\"login-qdzxCarousel\" arrow=\"never\">\r\n      <el-carousel-item v-for=\"item in loginImgList\" :key=\"item.id\">\r\n        <div class=\"loginTwoImg\"\r\n          :style=\"`background: url('${item.rollImgUrl}') no-repeat;background-size: cover;background-position: top;`\">\r\n        </div>\r\n      </el-carousel-item>\r\n    </el-carousel>\r\n    <div class=\"login-qdzx-box\">\r\n      <div class=\"login-qdzx-boxs\">\r\n        <div class=\"login-qdzx-logo-box\">\r\n          <div class=\"login-qdzx-logo\"></div>\r\n          <div class=\"login-qdzx-name\">{{ $generalName() }}</div>\r\n        </div>\r\n        <div class=\"login-qdzx-login-box\">\r\n          <xyl-sliding v-model=\"loginTab\" v-if=\"!forgetflag\">\r\n            <xyl-sliding-item value=\"1\">账号登录</xyl-sliding-item>\r\n            <xyl-sliding-item value=\"2\">扫码登录</xyl-sliding-item>\r\n          </xyl-sliding>\r\n          <div class=\"login-qdzx-form-box\" v-if=\"loginTab == '1' && !forgetflag\">\r\n            <el-form :model=\"loginForm\" :rules=\"rules\" class=\"loginzx-form\" ref=\"loginForm\" @submit.native.prevent>\r\n              <el-form-item prop=\"account\" class=\"loginzx-form-input\">\r\n                <el-input v-model=\"loginForm.account\" placeholder=\"账号/手机号/证件号码\" clearable></el-input>\r\n              </el-form-item>\r\n              <el-form-item prop=\"password\" class=\"loginzx-form-input\">\r\n                <el-input type=\"password\" show-password v-model=\"loginForm.password\" placeholder=\"密码\" clearable\r\n                  @keyup.enter.native=\"submitForm('loginForm')\"></el-input>\r\n              </el-form-item>\r\n              <el-form-item v-if=\"showVrify\" prop=\"verify\" class=\"loginzx-form-input\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"loginForm.verify\" placeholder=\"验证码\" clearable\r\n                      @keyup.enter.native=\"submitForm('loginForm')\">\r\n                    </el-input>\r\n                  </el-col>\r\n                  <el-col :span=\"10\">\r\n                    <el-button :disabled=\"setTimeoutNum ? true : false\" style=\"width: 100%\" round type=\"primary\"\r\n                      @click=\"sendCode('loginForm', 1)\">\r\n                      <span v-if=\"setTimeoutNum\">{{ setTimeoutNum }}</span>\r\n                      发送验证码\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n              <div class=\"form-slide-verify\" v-if=\"!showVrify\">\r\n                <slide-verify ref=\"slideblock\" :w=\"336\" :ws=\"21\" @success=\"onSuccess\" @again=\"onAgain\"\r\n                  :disabled=\"disabled\" slider-text=\"请拖动滑块至正确缺口\"></slide-verify>\r\n              </div>\r\n              <div class=\"loginzx-form-operation\">\r\n                <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n                <div class=\"loginzx-form-operation-box\">\r\n                  <div class=\"loginzx-form-operation-text\" @click=\"forgetflag = true\">\r\n                    忘记密码？\r\n                  </div>\r\n                  <!-- <div class=\"loginzx-form-operation-l\"></div>\r\n                <div class=\"loginzx-form-operation-text\">修改密码</div> -->\r\n                </div>\r\n              </div>\r\n              <el-button type=\"primary\" class=\"loginzx-form-button\" :loading=\"loading\"\r\n                @click=\"submitForm('loginForm')\">{{ loading ? '登录中' : '登录' }}</el-button>\r\n            </el-form>\r\n          </div>\r\n          <div class=\"login-qdzx-qr-box\" v-if=\"loginTab == '2' && !forgetflag\">\r\n            <div class=\"qr-code-box\" v-loading=\"qrloading\">\r\n              <vue-qr class=\"qr-code qrcode\" :margin=\"0\" :text=\"text\"></vue-qr>\r\n              <div class=\"refresh\" v-if=\"time && !qrloading\">\r\n                <div class=\"refresh-button\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"qr-code-box-text\">手机APP扫码登录</div>\r\n            <div class=\"qr-code-box-text-c\">扫码登录，更快，更安全</div>\r\n          </div>\r\n          <div v-if=\"forgetflag\">\r\n            <forgetpassword @gologin=\"gologin\"></forgetpassword>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"login-qdzx-text-box\">\r\n        <div class=\"login-qdzx-text\">\r\n          本系统仅用于非涉及党和国家秘密信息处理\r\n        </div>\r\n        <div class=\"login-qdzx-text\">技术支持:135 7313 9687</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"inform\" v-if=\"isShowIE\">\r\n      <div class=\"informBox\">\r\n        <div class=\"informName\">关于系统浏览器使用说明</div>\r\n        <div class=\"informText\">尊敬的青岛智慧政协平台用户：</div>\r\n        <div class=\"informText\">您好！青岛市智慧政协平台升级版正式上线。</div>\r\n        <div class=\"informText\">\r\n          由于微软公司推出的IE浏览器存在大量漏洞，且微软公司已停止对IE系列浏览器进行维护升级，在使用过程中存在信息安全隐患。为此本系统将不再兼容IE系列浏览器。\r\n        </div>\r\n        <div class=\"informText\">\r\n          为了您更好的使用体验及信息安全，建议优先选用国产浏览器及国际稳定版浏览器使用，推荐浏览器如下：火狐浏览器、360浏览器（使用极速模式）、Edge浏览器（win10自带浏览器）、谷歌浏览器等。\r\n        </div>\r\n        <div class=\"informText\">\r\n          如有任何疑问及使用问题，请致电信息化工作处0532-82937892或技术人员电话17854218732\r\n        </div>\r\n        <div class=\"downloadText\">\r\n          如本机尚未安装上述浏览器 可点击以下按钮自行下载\r\n        </div>\r\n        <div class=\"downloadBox\">\r\n          <el-button type=\"primary\" @click=\"btnback\">请点击此处跳转至下载中心！</el-button>\r\n          <!-- <div class=\"downloadItem\"\r\n               @click=\"browser('http://www.firefox.com.cn/')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            火狐浏览器\r\n          </div>\r\n          <div class=\"downloadItem\"\r\n               @click=\"browser('https://browser.360.cn/ee/')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            360浏览器\r\n          </div>\r\n          <div class=\"downloadItem\"\r\n               @click=\"browser('https://www.microsoft.com/zh-cn/edge')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            Edge浏览器\r\n          </div>\r\n          <div class=\"downloadItem\"\r\n               @click=\"browser('https://www.google.cn/chrome/thank-you.html?installdataindex=empty&statcb=1&defaultbrowser=0')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            谷歌浏览器\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { v1 as uuidv1 } from 'uuid'\r\nimport xylSliding from '@/components/zy-sliding/sliding'\r\nimport xylSlidingItem from '@/components/zy-sliding/sliding-item'\r\nimport vueQr from 'vue-qr'\r\nimport slideVerify from '@/components/slide-verify/slide-verify'\r\nimport forgetpassword from '../forgetPassword/forgetPassword'\r\nexport default {\r\n  name: 'login',\r\n  data () {\r\n    return {\r\n      loginImgList: [],\r\n      loginTab: '1',\r\n      forgetflag: false,\r\n      switchLogin: true,\r\n      checked: false,\r\n      valid: false,\r\n      disabled: false,\r\n      loginForm: {\r\n        account: '',\r\n        password: '',\r\n        verify: ''\r\n      },\r\n      rules: {\r\n        account: [\r\n          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        verify: []\r\n      },\r\n      loading: false,\r\n      text: '',\r\n      uuid: '',\r\n      time: false,\r\n      qrloading: false,\r\n      setTimeout: null,\r\n      isShowIE: false,\r\n      showVrify: false,\r\n      setTimeoutNum: 0\r\n    }\r\n  },\r\n  components: {\r\n    xylSliding,\r\n    xylSlidingItem,\r\n    vueQr,\r\n    slideVerify,\r\n    forgetpassword\r\n  },\r\n  created () {\r\n    if (!!window.ActiveXObject || 'ActiveXObject' in window) {\r\n      this.isShowIE = true\r\n    } else {\r\n      this.isShowIE = false\r\n    }\r\n    this.loginimg()\r\n    var userinfo = JSON.parse(localStorage.getItem('userinfo' + this.$logo())) || ''\r\n    if (userinfo) {\r\n      if (userinfo.checked) {\r\n        this.checked = userinfo.checked\r\n        this.loginForm.account = userinfo.account\r\n        this.loginForm.password = this.$utils.decrypt(userinfo.password, 'abcdefgabcdefg12')\r\n      }\r\n    }\r\n    this.getverify()\r\n  },\r\n  watch: {\r\n    loginTab (val) {\r\n      if (val === '2') {\r\n        this.refresh()\r\n      } else if (val === '1') {\r\n        this.disabled = false\r\n        clearTimeout(this.setTimeout)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    btnback () {\r\n      // const routeData = this.$router.resolve({ path: '/login-help' })\r\n      window.open('#/login-help', '_blank')\r\n    },\r\n    async getverify () {\r\n      const res = await this.$api.general.shortcodeEnable()\r\n      var { data } = res\r\n      this.showVrify = data\r\n    },\r\n    async sendCode (formName, num) {\r\n      this.$refs[formName].validate(async (valid) => {\r\n        console.log('valid===>', valid)\r\n        if (num === 1) {\r\n          const res = await this.$api.general.shortcodeSend({\r\n            account: this.loginForm.account,\r\n            password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002')\r\n          })\r\n          var { errcode } = res\r\n          if (errcode === 200) {\r\n            this.setTimeoutNum = 60\r\n            setInterval(() => {\r\n              if (this.setTimeoutNum > 0) this.setTimeoutNum--\r\n            }, 1000)\r\n            this.$message({\r\n              message: '验证码已发送，请注意查收！',\r\n              type: 'success'\r\n            })\r\n          }\r\n        } else {\r\n          if (valid) {\r\n            const res = await this.$api.general.shortcodeSend({\r\n              account: this.loginForm.account,\r\n              password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002')\r\n            })\r\n            var { errcodes } = res\r\n            if (errcodes === 200) {\r\n              this.setTimeoutNum = 60\r\n              setInterval(() => {\r\n                if (this.setTimeoutNum > 0) this.setTimeoutNum--\r\n              }, 1000)\r\n              this.$message({\r\n                message: '验证码已发送，请注意查收！',\r\n                type: 'success'\r\n              })\r\n            }\r\n          } else {\r\n            return false\r\n          }\r\n        }\r\n      })\r\n    },\r\n    async loginimg () {\r\n      const res = await this.$api.general.loginimg()\r\n      var { data } = res\r\n      this.loginImgList = data\r\n    },\r\n    gologin () {\r\n      this.forgetflag = false\r\n    },\r\n    onSuccess (times) {\r\n      // console.log('验证通过')\r\n      this.valid = true\r\n      this.disabled = true\r\n    },\r\n    onAgain () {\r\n      this.$message.error('检测到非人为操作的哦！')\r\n      this.handleClick()\r\n    },\r\n    handleClick () {\r\n      this.disabled = false\r\n      this.$refs.slideblock.reset()\r\n    },\r\n    switchMethods (type) {\r\n      if (this.switchLogin === type) {\r\n        return\r\n      }\r\n      this.switchLogin = type\r\n      if (!this.switchLogin) {\r\n        this.refresh()\r\n      } else {\r\n        this.disabled = false\r\n        sessionStorage.clear()\r\n        clearTimeout(this.setTimeout)\r\n      }\r\n    },\r\n    async refresh () {\r\n      const res = await this.$api.general.nologin({\r\n        codes: 'rongCloudIdPrefix'\r\n      })\r\n      console.log(res)\r\n      var { data } = res\r\n      this.time = false\r\n      this.uuid = uuidv1()\r\n      this.text = data.rongCloudIdPrefix + '|login|' + this.uuid\r\n      this.apptoken()\r\n      this.setTimeout = setTimeout(() => {\r\n        this.time = true\r\n      }, 180000)\r\n    },\r\n    async apptoken () {\r\n      const res = await this.$api.general.apptoken({\r\n        qrCodeId: this.uuid\r\n      })\r\n      if (res.data === '' && this.loginTab === '2' && !this.time) {\r\n        setTimeout(() => {\r\n          this.apptoken()\r\n        }, 2000)\r\n      }\r\n      if (res.data) {\r\n        this.qrloading = true\r\n        clearTimeout(this.setTimeout)\r\n        this.time = true\r\n        var { data, projects } = res\r\n        this.$message({\r\n          message: '登录成功!',\r\n          type: 'success'\r\n        })\r\n        sessionStorage.setItem('token' + this.$logo(), JSON.stringify(data))\r\n        sessionStorage.setItem('projects' + this.$logo(), JSON.stringify(projects))\r\n        if (this.$isMoreProject()) {\r\n          if (projects.length === 1) {\r\n            this.$switchClick(projects[0].hostAddr)\r\n          } else {\r\n            this.$router.push({ path: '/switchpage' })\r\n          }\r\n        } else {\r\n          if (projects.length) {\r\n            projects.forEach(item => {\r\n              if (item.hostAddr === this.$api.general.baseURL()) {\r\n                console.log(item.hostAddr)\r\n                this.$switchClick(item.hostAddr)\r\n              }\r\n            })\r\n          } else {\r\n            this.$switchClick(this.$api.general.baseURL())\r\n          }\r\n        }\r\n      }\r\n    },\r\n    success () {\r\n      this.valid = true\r\n    },\r\n    submitForm (formName) {\r\n      this.rules.verify.push({ required: true, message: '请输入验证码', trigger: 'blur' })\r\n      this.$refs[formName].validate(async (valid) => {\r\n        if (valid) {\r\n          // if (this.showVrify) {\r\n          //   const res2 = await this.$api.general.shortcodeVerify({\r\n          //     account: this.loginForm.account,\r\n          //     verifyCode: this.loginForm.verify\r\n          //   })\r\n          //   const { errcode } = res2\r\n          //   if (errcode !== 200) {\r\n          //     this.$message.error(errcode)\r\n          //     return\r\n          //   }\r\n          // }\r\n          this.loading = true\r\n          if (!this.valid && !this.showVrify) {\r\n            this.loading = false\r\n            this.$message.error('请先通过验证在登录!')\r\n            return\r\n          }\r\n          this.$api.general.loginUc({\r\n            username: this.loginForm.account,\r\n            password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002'),\r\n            verifyCode: this.loginForm.verify\r\n          }).then(res => {\r\n            var { data, projects } = res\r\n            this.$message({\r\n              message: '登录成功!',\r\n              type: 'success'\r\n            })\r\n            sessionStorage.setItem('token' + this.$logo(), JSON.stringify(data))\r\n            sessionStorage.setItem('projects' + this.$logo(), JSON.stringify(projects))\r\n            if (this.$isMoreProject()) {\r\n              if (projects.length === 1) {\r\n                this.$switchClick(projects[0].hostAddr)\r\n              } else {\r\n                this.$router.push({ path: '/switchpage' })\r\n              }\r\n            } else {\r\n              if (projects.length) {\r\n                projects.forEach(item => {\r\n                  if (item.hostAddr === this.$api.general.baseURL()) {\r\n                    console.log(item.hostAddr)\r\n                    this.$switchClick(item.hostAddr)\r\n                  }\r\n                })\r\n              } else {\r\n                this.$switchClick(this.$api.general.baseURL())\r\n              }\r\n            }\r\n            const userinfo = { checked: this.checked, account: this.loginForm.account, password: this.$utils.encrypt(this.loginForm.password, 'abcdefgabcdefg12') }\r\n            localStorage.setItem('userinfo' + this.$logo(), JSON.stringify(userinfo))\r\n          }).catch(() => {\r\n            this.loading = false\r\n            this.$message.error('登录失败!')\r\n          })\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    browser (url) {\r\n      window.open(url, '_blank')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./login-qdzx.scss\";\r\n</style>\r\n"]}]}