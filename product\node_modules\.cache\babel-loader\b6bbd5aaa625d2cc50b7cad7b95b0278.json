{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\forgetPassword\\forgetPassword.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\forgetPassword\\forgetPassword.vue", "mtime": 1755833016358}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHZhbGlkIGZyb20gJ0AvY29tcG9uZW50cy92YWxpZC92YWxpZCc7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICB2YWxpZAogIH0sCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB2YWxpZENvZGU6ICcnLAogICAgICBmb3JnZXRGb3JtOiB7CiAgICAgICAgYWNjb3VudDogJycsCiAgICAgICAgdmFsaWRDb2RlOiAnJywKICAgICAgICBzbXNWYWxpZGF0aW9uOiAnJywKICAgICAgICBuZXdQYXNzd29yZDogJycsCiAgICAgICAgbmV3UGFzc3dvcmRBZ2lhbjogJycKICAgICAgfSwKICAgICAgZm9yZ2V0cnVsZXM6IHsKICAgICAgICBhY2NvdW50OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl6LSm5Y+3L+aJi+acuuWPty/or4Hku7blj7fnoIEnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgbmV3UGFzc3dvcmQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXlr4bnoIEnLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfV0sCiAgICAgICAgbmV3UGFzc3dvcmRBZ2lhbjogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeWvhueggScsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBzbXNWYWxpZGF0aW9uOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAn6K+36L6T5YWl55+t5L+h6aqM6K+B56CBJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIHNtc1Nob3c6IGZhbHNlLAogICAgICBzbXNUZXh0OiAn6I635Y+W6aqM6K+B56CBJywKICAgICAgY291bnRkb3duOiAwCiAgICB9OwogIH0sCgogIG1ldGhvZHM6IHsKICAgIC8vIOWvhueggemqjOivgeWHveaVsO+8mjjkvY3ku6XkuIrvvIzlpKflsI/lhpnlrZfmr43jgIHmlbDlrZfjgIHnrKblj7foh7PlsJHljIXlkKsz56eNCiAgICB2YWxpZGF0ZVBhc3N3b3JkKHBhc3N3b3JkKSB7CiAgICAgIGlmIChwYXNzd29yZC5sZW5ndGggPCA4KSB7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CgogICAgICBsZXQgdHlwZUNvdW50ID0gMDsgLy8g5qOA5p+l5piv5ZCm5YyF5ZCr5bCP5YaZ5a2X5q+NCgogICAgICBpZiAoL1thLXpdLy50ZXN0KHBhc3N3b3JkKSkgewogICAgICAgIHR5cGVDb3VudCsrOwogICAgICB9IC8vIOajgOafpeaYr+WQpuWMheWQq+Wkp+WGmeWtl+avjQoKCiAgICAgIGlmICgvW0EtWl0vLnRlc3QocGFzc3dvcmQpKSB7CiAgICAgICAgdHlwZUNvdW50Kys7CiAgICAgIH0gLy8g5qOA5p+l5piv5ZCm5YyF5ZCr5pWw5a2XCgoKICAgICAgaWYgKC9bMC05XS8udGVzdChwYXNzd29yZCkpIHsKICAgICAgICB0eXBlQ291bnQrKzsKICAgICAgfSAvLyDmo4Dmn6XmmK/lkKbljIXlkKvnrKblj7cKCgogICAgICBpZiAoL1t+IUAjJCVeJiooKV8rXC09W1xde307JzoiXFx8LC48Pi8/XS8udGVzdChwYXNzd29yZCkpIHsKICAgICAgICB0eXBlQ291bnQrKzsKICAgICAgfQoKICAgICAgcmV0dXJuIHR5cGVDb3VudCA+PSAzOwogICAgfSwKCiAgICBmb3JnZXQoKSB7CiAgICAgIC8vIHRoaXMuJHJvdXRlci5wdXNoKHsgbmFtZTogJ2NoYW5nZXBhc3N3b3JkJyB9KQogICAgICB0aGlzLmZvcmdldGZsYWcgPSB0cnVlOwogICAgICB0aGlzLmZvcmdldEZvcm0uYWNjb3VudCA9IHRoaXMubG9naW5Gb3JtLmFjY291bnQ7CiAgICAgIHRoaXMubG9naW5Gb3JtLnNtc1ZhbGlkYXRpb24gPSAnJzsKICAgICAgdGhpcy5jb3VudGRvd24gPSAwOwogICAgfSwKCiAgICBnb2xvZ2luKCkgewogICAgICAvLyB0aGlzLmNvdW50ZG93biA9IDAKICAgICAgLy8gdGhpcy5mb3JnZXRmbGFnID0gZmFsc2UKICAgICAgdGhpcy4kZW1pdCgnZ29sb2dpbicpOwogICAgfSwKCiAgICB2YWxpZENvZGV0ZXN0aW5nKCkgewogICAgICBpZiAodGhpcy5mb3JnZXRGb3JtLmFjY291bnQgPT09ICcnKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn6K+35YWI6L6T5YWl6LSm5Y+3L+aJi+acuuWPty/or4Hku7blj7fnoIHvvIEnLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICBpZiAodGhpcy5mb3JnZXRGb3JtLnZhbGlkQ29kZS50b0xvd2VyQ2FzZSgpICE9PSB0aGlzLnZhbGlkQ29kZS50b0xvd2VyQ2FzZSgpKSB7CiAgICAgICAgdGhpcy5mb3JnZXRGb3JtLnZhbGlkQ29kZSA9ICcnOwogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6aqM6K+B56CB5LiN5q2j56GuIScpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgaWYgKCF0aGlzLnZhbGlkYXRlUGFzc3dvcmQodGhpcy5mb3JnZXRGb3JtLm5ld1Bhc3N3b3JkKSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+WvhueggeiHs+WwkeWcqDjkvY3ku6XkuIrvvIzlpKflsI/lhpnlrZfmr43jgIHmlbDlrZfjgIHnrKblj7foh7PlsJHljIXlkKsz56eN44CCJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgaWYgKHRoaXMuZm9yZ2V0Rm9ybS5uZXdQYXNzd29yZEFnaWFuICE9PSB0aGlzLmZvcmdldEZvcm0ubmV3UGFzc3dvcmQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfkuKTmrKHlr4bnoIHovpPlhaXkuI3kuIDoh7TvvIEnLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CgogICAgICB0aGlzLiRhcGkuZ2VuZXJhbC5zZW5kbWVzc2FnZSh7CiAgICAgICAgYWNjb3VudDogdGhpcy5mb3JnZXRGb3JtLmFjY291bnQsCiAgICAgICAgcGFzc3dvcmQ6ICcnCiAgICAgIH0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLmNvdW50ZG93biA9IDYwOwogICAgICAgIHRoaXMuc2V0dGltZSgpOwogICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+efreS/oeWPkemAgeaIkOWKnyEnLAogICAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfSwKCiAgICBlZGl0cHdkKGZvcm1OYW1lKSB7CiAgICAgIHRoaXMuJHJlZnNbZm9ybU5hbWVdLnZhbGlkYXRlKHZhbGlkID0+IHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmICh0aGlzLmZvcmdldEZvcm0udmFsaWRDb2RlLnRvTG93ZXJDYXNlKCkgIT09IHRoaXMudmFsaWRDb2RlLnRvTG93ZXJDYXNlKCkpIHsKICAgICAgICAgICAgdGhpcy5mb3JnZXRGb3JtLnZhbGlkQ29kZSA9ICcnOwogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+mqjOivgeeggeS4jeato+ehriEnKTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQoKICAgICAgICAgIGlmICghdGhpcy52YWxpZGF0ZVBhc3N3b3JkKHRoaXMuZm9yZ2V0Rm9ybS5uZXdQYXNzd29yZCkpIHsKICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICAgICAgbWVzc2FnZTogJ+WvhueggeiHs+WwkeWcqDjkvY3ku6XkuIrvvIzlpKflsI/lhpnlrZfmr43jgIHmlbDlrZfjgIHnrKblj7foh7PlsJHljIXlkKsz56eN44CCJywKICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAodGhpcy5mb3JnZXRGb3JtLm5ld1Bhc3N3b3JkQWdpYW4gIT09IHRoaXMuZm9yZ2V0Rm9ybS5uZXdQYXNzd29yZCkgewogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICAgICAgICBtZXNzYWdlOiAn5Lik5qyh5a+G56CB6L6T5YWl5LiN5LiA6Ie077yBJywKICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgIH0KCiAgICAgICAgICBpZiAodGhpcy5mb3JnZXRGb3JtLnNtc1ZhbGlkYXRpb24gPT09ICcnKSB7CiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UoewogICAgICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXpqozor4HnoIHvvIEnLAogICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICB9KTsKICAgICAgICAgICAgcmV0dXJuOwogICAgICAgICAgfQoKICAgICAgICAgIHRoaXMuJGFwaS5nZW5lcmFsLmVkaXRwd2QoewogICAgICAgICAgICB1c2VybmFtZTogdGhpcy5mb3JnZXRGb3JtLmFjY291bnQsCiAgICAgICAgICAgIHBhc3N3b3JkOiB0aGlzLiR1dGlscy5lbmNyeXB0KHRoaXMuZm9yZ2V0Rm9ybS5uZXdQYXNzd29yZCwgJ3p5c29mdGhuengyMDIwMDInKSwKICAgICAgICAgICAgdmVyaWZ5Q29kZTogdGhpcy5mb3JnZXRGb3JtLnNtc1ZhbGlkYXRpb24KICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAoKICAgIHNldHRpbWUoKSB7CiAgICAgIGlmICh0aGlzLmNvdW50ZG93biA9PT0gMCkgewogICAgICAgIHRoaXMuc21zVGV4dCA9ICfojrflj5bpqozor4HnoIEnOwogICAgICAgIHRoaXMuY291bnRkb3duID0gNjA7CiAgICAgICAgcmV0dXJuOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuc21zVGV4dCA9ICfph43mlrDlj5HpgIEnICsgdGhpcy5jb3VudGRvd24gKyAnUyc7CiAgICAgICAgdGhpcy5jb3VudGRvd24tLTsKICAgICAgfQoKICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgdGhpcy5zZXR0aW1lKCk7CiAgICAgIH0sIDEwMDApOwogICAgfQoKICB9Cn07"}, {"version": 3, "mappings": "AAiDA;AACA;EACAA;IAAAC;EAAA,CADA;;EAEAC;IACA;MACAC,aADA;MAEAC;QACAC,WADA;QAEAF,aAFA;QAGAG,iBAHA;QAIAC,eAJA;QAKAC;MALA,CAFA;MASAC;QACAJ,UACA;UAAAK;UAAAC;UAAAC;QAAA,CADA,CADA;QAIAL,cACA;UAAAG;UAAAC;UAAAC;QAAA,CADA,CAJA;QAOAJ,mBACA;UAAAE;UAAAC;UAAAC;QAAA,CADA,CAPA;QAUAN,eACA;UAAAI;UAAAC;UAAAC;QAAA;MAXA,CATA;MAsBAC,cAtBA;MAuBAC,gBAvBA;MAwBAC;IAxBA;EA0BA,CA7BA;;EA8BAC;IACA;IACAC;MACA;QACA;MACA;;MAEA,kBALA,CAOA;;MACA;QACAC;MACA,CAVA,CAYA;;;MACA;QACAA;MACA,CAfA,CAiBA;;;MACA;QACAA;MACA,CApBA,CAsBA;;;MACA;QACAA;MACA;;MAEA;IACA,CA9BA;;IAgCAC;MACA;MACA;MACA;MACA;MACA;IACA,CAtCA;;IAuCAC;MACA;MACA;MACA;IACA,CA3CA;;IA4CAC;MACA;QACA;UACAV,2BADA;UAEAW;QAFA;QAIA;MACA;;MACA;QACA;QACA;QACA;MACA;;MACA;QACA;UACAX,uCADA;UAEAW;QAFA;QAIA;MACA;;MAEA;QACA;UACAX,qBADA;UAEAW;QAFA;QAIA;MACA;;MACA;QACAjB,gCADA;QAEAkB;MAFA,GAGAC,IAHA,CAGAC;QACA;QACA;QACA;UACAd,kBADA;UAEAW;QAFA;MAIA,CAVA;IAWA,CAnFA;;IAoFAI;MACA;QACA;UACA;YACA;YACA;YACA;UACA;;UACA;YACA;cACAf,uCADA;cAEAW;YAFA;YAIA;UACA;;UAEA;YACA;cACAX,qBADA;cAEAW;YAFA;YAIA;UACA;;UACA;YACA;cACAX,kBADA;cAEAW;YAFA;YAIA;UACA;;UACA;YACAK,iCADA;YAEAJ,8EAFA;YAGAK;UAHA;QAKA;MACA,CAnCA;IAoCA,CAzHA;;IA0HAC;MACA;QACA;QACA;QACA;MACA,CAJA,MAIA;QACA;QACA;MACA;;MACAC;QACA;MACA,CAFA,EAEA,IAFA;IAGA;;EAtIA;AA9BA", "names": ["components", "valid", "data", "validCode", "forgetForm", "account", "smsValidation", "newPassword", "newPasswordAgian", "<PERSON><PERSON><PERSON>", "required", "message", "trigger", "smsShow", "smsText", "countdown", "methods", "validatePassword", "typeCount", "forget", "gologin", "validCodetesting", "type", "password", "then", "res", "editpwd", "username", "verifyCode", "settime", "setTimeout"], "sourceRoot": "src/views/main/forgetPassword", "sources": ["forgetPassword.vue"], "sourcesContent": ["<template>\r\n  <div class=\"login-form-box-pass\">\r\n    <div class=\"forgetText\">密码至少在8位以上，大小写字母、数字、符号至少包含3种。</div>\r\n    <el-form :model=\"forgetForm\" :rules=\"forgetrules\" class=\"login-form\" ref=\"forgetForm\" @submit.native.prevent>\r\n      <el-form-item prop=\"account\" class=\"login-form-input\">\r\n        <el-input v-model=\"forgetForm.account\" placeholder=\"账号/手机号/证件号码\" clearable></el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item class=\"login-form-input form-valid\">\r\n        <el-input v-model=\"forgetForm.validCode\" placeholder=\"验证码\" clearable> </el-input>\r\n        <valid :length=\"4\" :heigth=\"'36px'\" :value.sync=\"validCode\"></valid>\r\n      </el-form-item>\r\n      <!-- <div class=\"form-valid\">\r\n        <el-input\r\n          v-model=\"forgetForm.validCode\"\r\n          placeholder=\"验证码\"\r\n          clearable\r\n        > </el-input>\r\n        <valid\r\n          :length=\"4\"\r\n          :heigth=\"'36px'\"\r\n          :value.sync=\"validCode\"\r\n        ></valid>\r\n      </div> -->\r\n      <el-form-item prop=\"newPassword\" class=\"login-form-input\">\r\n\r\n        <el-input type=\"password\" v-model=\"forgetForm.newPassword\" placeholder=\"请输入新密码\" clearable></el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"newPassword\" class=\"login-form-input\">\r\n\r\n        <el-input type=\"password\" v-model=\"forgetForm.newPasswordAgian\" placeholder=\"请再次输入新密码\" clearable></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"login-form-input form-valid\">\r\n        <el-input v-model=\"forgetForm.smsValidation\" placeholder=\"短信验证码\" clearable> </el-input>\r\n        <el-button type=\"primary\" @click=\"validCodetesting\" :disabled=\"smsText != '获取验证码'\"> {{ smsText }} </el-button>\r\n      </el-form-item>\r\n      <div class=\"login-form-operation\">\r\n        <div class=\"login-form-operation-box\">\r\n          <div class=\"login-form-operation-text\" @click=\"gologin\">返回登录</div>\r\n        </div>\r\n      </div>\r\n      <el-button type=\"primary\" class=\"login-form-button\" @click=\"editpwd('forgetForm')\"> 确定 </el-button>\r\n    </el-form>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport valid from '@/components/valid/valid'\r\nexport default {\r\n  components: { valid },\r\n  data () {\r\n    return {\r\n      validCode: '',\r\n      forgetForm: {\r\n        account: '',\r\n        validCode: '',\r\n        smsValidation: '',\r\n        newPassword: '',\r\n        newPasswordAgian: ''\r\n      },\r\n      forgetrules: {\r\n        account: [\r\n          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        newPasswordAgian: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        smsValidation:\r\n          [{ required: true, message: '请输入短信验证码', trigger: 'blur' }]\r\n      },\r\n      smsShow: false,\r\n      smsText: '获取验证码',\r\n      countdown: 0\r\n    }\r\n  },\r\n  methods: {\r\n    // 密码验证函数：8位以上，大小写字母、数字、符号至少包含3种\r\n    validatePassword (password) {\r\n      if (password.length < 8) {\r\n        return false\r\n      }\r\n\r\n      let typeCount = 0\r\n\r\n      // 检查是否包含小写字母\r\n      if (/[a-z]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含大写字母\r\n      if (/[A-Z]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含数字\r\n      if (/[0-9]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含符号\r\n      if (/[~!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      return typeCount >= 3\r\n    },\r\n\r\n    forget () {\r\n      // this.$router.push({ name: 'changepassword' })\r\n      this.forgetflag = true\r\n      this.forgetForm.account = this.loginForm.account\r\n      this.loginForm.smsValidation = ''\r\n      this.countdown = 0\r\n    },\r\n    gologin () {\r\n      // this.countdown = 0\r\n      // this.forgetflag = false\r\n      this.$emit('gologin')\r\n    },\r\n    validCodetesting () {\r\n      if (this.forgetForm.account === '') {\r\n        this.$message({\r\n          message: '请先输入账号/手机号/证件号码！',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {\r\n        this.forgetForm.validCode = ''\r\n        this.$message.warning('验证码不正确!')\r\n        return\r\n      }\r\n      if (!this.validatePassword(this.forgetForm.newPassword)) {\r\n        this.$message({\r\n          message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {\r\n        this.$message({\r\n          message: '两次密码输入不一致！',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$api.general.sendmessage({\r\n        account: this.forgetForm.account,\r\n        password: ''\r\n      }).then(res => {\r\n        this.countdown = 60\r\n        this.settime()\r\n        this.$message({\r\n          message: '短信发送成功!',\r\n          type: 'success'\r\n        })\r\n      })\r\n    },\r\n    editpwd (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {\r\n            this.forgetForm.validCode = ''\r\n            this.$message.warning('验证码不正确!')\r\n            return\r\n          }\r\n          if (!this.validatePassword(this.forgetForm.newPassword)) {\r\n            this.$message({\r\n              message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {\r\n            this.$message({\r\n              message: '两次密码输入不一致！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n          if (this.forgetForm.smsValidation === '') {\r\n            this.$message({\r\n              message: '请输入验证码！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n          this.$api.general.editpwd({\r\n            username: this.forgetForm.account,\r\n            password: this.$utils.encrypt(this.forgetForm.newPassword, 'zysofthnzx202002'),\r\n            verifyCode: this.forgetForm.smsValidation\r\n          })\r\n        }\r\n      })\r\n    },\r\n    settime () {\r\n      if (this.countdown === 0) {\r\n        this.smsText = '获取验证码'\r\n        this.countdown = 60\r\n        return\r\n      } else {\r\n        this.smsText = '重新发送' + this.countdown + 'S'\r\n        this.countdown--\r\n      }\r\n      setTimeout(() => {\r\n        this.settime()\r\n      }, 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scpoed>\r\n// @import './login.scss';\r\n.forgetText {\r\n  font-size: $textSize12;\r\n  line-height: 20px;\r\n}\r\n\r\n.login-form-box-pass {\r\n  padding: 24px;\r\n  min-height: 326px;\r\n}\r\n\r\n.login-form {\r\n  padding-top: 12px;\r\n\r\n  .login-form-input {\r\n    margin-bottom: 19px;\r\n\r\n    .el-form-item__content {\r\n      line-height: 36px;\r\n\r\n      .el-input__inner {\r\n        height: 36px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-valid {\r\n    width: 100%;\r\n    height: 36px;\r\n    margin-bottom: 19px;\r\n\r\n    .el-form-item__content {\r\n      display: flex;\r\n    }\r\n\r\n    .el-input {\r\n      width: 154px;\r\n      height: 36px;\r\n\r\n      input {\r\n        height: 36px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n\r\n    .ValidCode {\r\n      background-color: #ccc;\r\n      margin-left: 20px;\r\n    }\r\n\r\n    .el-button {\r\n      margin-left: 20px;\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .login-form-operation {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 24px;\r\n\r\n    .login-form-operation-box {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n\r\n      .login-form-operation-text {\r\n        font-size: $textSize12;\r\n        line-height: 14px;\r\n        font-family: SimSun;\r\n        color: $zy-color;\r\n        font-weight: 400;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .login-form-operation-l {\r\n        width: 1px;\r\n        height: 14px;\r\n        margin: 0 9px;\r\n        background-color: #e4dee0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .login-form-button {\r\n    width: 100%;\r\n    background-color: $zy-color;\r\n    padding: 0;\r\n    height: 38px;\r\n    line-height: 38px;\r\n  }\r\n}\r\n\r\n.form-valid {\r\n  width: 100%;\r\n  height: 36px;\r\n  margin-bottom: 19px;\r\n\r\n  .el-form-item__content {\r\n    display: flex;\r\n  }\r\n\r\n  .el-input {\r\n    width: 154px;\r\n    height: 36px;\r\n\r\n    input {\r\n      height: 36px;\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .ValidCode {\r\n    background-color: #ccc;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .el-button {\r\n    margin-left: 20px;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}