{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\login-qdzx\\login-qdzx.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\login-qdzx\\login-qdzx.vue", "mtime": 1755832720145}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login-qdzx.vue"], "names": [], "mappings": ";AAoIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login-qdzx.vue", "sourceRoot": "src/views/main/login-qdzx", "sourcesContent": ["<template>\r\n  <div class=\"login-qdzx\">\r\n    <el-carousel class=\"login-qdzxCarousel\" arrow=\"never\">\r\n      <el-carousel-item v-for=\"item in loginImgList\" :key=\"item.id\">\r\n        <div class=\"loginTwoImg\"\r\n          :style=\"`background: url('${item.rollImgUrl}') no-repeat;background-size: cover;background-position: top;`\">\r\n        </div>\r\n      </el-carousel-item>\r\n    </el-carousel>\r\n    <div class=\"login-qdzx-box\">\r\n      <div class=\"login-qdzx-boxs\">\r\n        <div class=\"login-qdzx-logo-box\">\r\n          <div class=\"login-qdzx-logo\"></div>\r\n          <div class=\"login-qdzx-name\">{{ $generalName() }}</div>\r\n        </div>\r\n        <div class=\"login-qdzx-login-box\">\r\n          <xyl-sliding v-model=\"loginTab\" v-if=\"!forgetflag\">\r\n            <xyl-sliding-item value=\"1\">账号登录</xyl-sliding-item>\r\n            <xyl-sliding-item value=\"2\">扫码登录</xyl-sliding-item>\r\n          </xyl-sliding>\r\n          <div class=\"login-qdzx-form-box\" v-if=\"loginTab == '1' && !forgetflag\">\r\n            <el-form :model=\"loginForm\" :rules=\"rules\" class=\"loginzx-form\" ref=\"loginForm\" @submit.native.prevent>\r\n              <el-form-item prop=\"account\" class=\"loginzx-form-input\">\r\n                <el-input v-model=\"loginForm.account\" placeholder=\"账号/手机号/证件号码\" clearable></el-input>\r\n              </el-form-item>\r\n              <el-form-item prop=\"password\" class=\"loginzx-form-input\">\r\n                <el-input type=\"password\" show-password v-model=\"loginForm.password\" placeholder=\"密码\" clearable\r\n                  @keyup.enter.native=\"submitForm('loginForm')\"></el-input>\r\n              </el-form-item>\r\n              <el-form-item v-if=\"showVrify\" prop=\"verify\" class=\"loginzx-form-input\">\r\n                <el-row :gutter=\"20\">\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"loginForm.verify\" placeholder=\"验证码\" clearable\r\n                      @keyup.enter.native=\"submitForm('loginForm')\">\r\n                    </el-input>\r\n                  </el-col>\r\n                  <el-col :span=\"10\">\r\n                    <el-button :disabled=\"setTimeoutNum ? true : false\" style=\"width: 100%\" round type=\"primary\"\r\n                      @click=\"sendCode('loginForm', 1)\">\r\n                      <span v-if=\"setTimeoutNum\">{{ setTimeoutNum }}</span>\r\n                      发送验证码\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n              <div class=\"form-slide-verify\" v-if=\"!showVrify\">\r\n                <slide-verify ref=\"slideblock\" :w=\"336\" :ws=\"21\" @success=\"onSuccess\" @again=\"onAgain\"\r\n                  :disabled=\"disabled\" slider-text=\"请拖动滑块至正确缺口\"></slide-verify>\r\n              </div>\r\n              <div class=\"loginzx-form-operation\">\r\n                <el-checkbox v-model=\"checked\">记住用户名和密码</el-checkbox>\r\n                <div class=\"loginzx-form-operation-box\">\r\n                  <div class=\"loginzx-form-operation-text\" @click=\"forgetflag = true\">\r\n                    忘记密码？\r\n                  </div>\r\n                  <!-- <div class=\"loginzx-form-operation-l\"></div>\r\n                <div class=\"loginzx-form-operation-text\">修改密码</div> -->\r\n                </div>\r\n              </div>\r\n              <el-button type=\"primary\" class=\"loginzx-form-button\" :loading=\"loading\"\r\n                @click=\"submitForm('loginForm')\">{{ loading ? '登录中' : '登录' }}</el-button>\r\n            </el-form>\r\n          </div>\r\n          <div class=\"login-qdzx-qr-box\" v-if=\"loginTab == '2' && !forgetflag\">\r\n            <div class=\"qr-code-box\" v-loading=\"qrloading\">\r\n              <vue-qr class=\"qr-code qrcode\" :margin=\"0\" :text=\"text\"></vue-qr>\r\n              <div class=\"refresh\" v-if=\"time && !qrloading\">\r\n                <div class=\"refresh-button\">\r\n                  <el-button type=\"primary\" size=\"small\" @click=\"refresh\">刷新</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"qr-code-box-text\">手机APP扫码登录</div>\r\n            <div class=\"qr-code-box-text-c\">扫码登录，更快，更安全</div>\r\n          </div>\r\n          <div v-if=\"forgetflag\">\r\n            <forgetpassword @gologin=\"gologin\"></forgetpassword>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"login-qdzx-text-box\">\r\n        <div class=\"login-qdzx-text\">\r\n          本系统仅用于非涉及党和国家秘密信息处理\r\n        </div>\r\n        <div class=\"login-qdzx-text\">技术支持:135 7313 9687</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"inform\" v-if=\"isShowIE\">\r\n      <div class=\"informBox\">\r\n        <div class=\"informName\">关于系统浏览器使用说明</div>\r\n        <div class=\"informText\">尊敬的青岛智慧政协平台用户：</div>\r\n        <div class=\"informText\">您好！青岛市智慧政协平台升级版正式上线。</div>\r\n        <div class=\"informText\">\r\n          由于微软公司推出的IE浏览器存在大量漏洞，且微软公司已停止对IE系列浏览器进行维护升级，在使用过程中存在信息安全隐患。为此本系统将不再兼容IE系列浏览器。\r\n        </div>\r\n        <div class=\"informText\">\r\n          为了您更好的使用体验及信息安全，建议优先选用国产浏览器及国际稳定版浏览器使用，推荐浏览器如下：火狐浏览器、360浏览器（使用极速模式）、Edge浏览器（win10自带浏览器）、谷歌浏览器等。\r\n        </div>\r\n        <div class=\"informText\">\r\n          如有任何疑问及使用问题，请致电信息化工作处0532-82937892或技术人员电话17854218732\r\n        </div>\r\n        <div class=\"downloadText\">\r\n          如本机尚未安装上述浏览器 可点击以下按钮自行下载\r\n        </div>\r\n        <div class=\"downloadBox\">\r\n          <el-button type=\"primary\" @click=\"btnback\">请点击此处跳转至下载中心！</el-button>\r\n          <!-- <div class=\"downloadItem\"\r\n               @click=\"browser('http://www.firefox.com.cn/')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            火狐浏览器\r\n          </div>\r\n          <div class=\"downloadItem\"\r\n               @click=\"browser('https://browser.360.cn/ee/')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            360浏览器\r\n          </div>\r\n          <div class=\"downloadItem\"\r\n               @click=\"browser('https://www.microsoft.com/zh-cn/edge')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            Edge浏览器\r\n          </div>\r\n          <div class=\"downloadItem\"\r\n               @click=\"browser('https://www.google.cn/chrome/thank-you.html?installdataindex=empty&statcb=1&defaultbrowser=0')\">\r\n            <div class=\"downloadIcon\"></div>\r\n            谷歌浏览器\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { v1 as uuidv1 } from 'uuid'\r\nimport xylSliding from '@/components/zy-sliding/sliding'\r\nimport xylSlidingItem from '@/components/zy-sliding/sliding-item'\r\nimport vueQr from 'vue-qr'\r\nimport slideVerify from '@/components/slide-verify/slide-verify'\r\nimport forgetpassword from '../forgetPassword/forgetPassword'\r\nexport default {\r\n  name: 'login',\r\n  data () {\r\n    return {\r\n      loginImgList: [],\r\n      loginTab: '1',\r\n      forgetflag: false,\r\n      switchLogin: true,\r\n      checked: false,\r\n      valid: false,\r\n      disabled: false,\r\n      loginForm: {\r\n        account: '',\r\n        password: '',\r\n        verify: ''\r\n      },\r\n      rules: {\r\n        account: [\r\n          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }\r\n        ],\r\n        password: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        verify: []\r\n      },\r\n      loading: false,\r\n      text: '',\r\n      uuid: '',\r\n      time: false,\r\n      qrloading: false,\r\n      setTimeout: null,\r\n      isShowIE: false,\r\n      showVrify: false,\r\n      setTimeoutNum: 0\r\n    }\r\n  },\r\n  components: {\r\n    xylSliding,\r\n    xylSlidingItem,\r\n    vueQr,\r\n    slideVerify,\r\n    forgetpassword\r\n  },\r\n  created () {\r\n    if (!!window.ActiveXObject || 'ActiveXObject' in window) {\r\n      this.isShowIE = true\r\n    } else {\r\n      this.isShowIE = false\r\n    }\r\n    this.loginimg()\r\n    var userinfo = JSON.parse(localStorage.getItem('userinfo' + this.$logo())) || ''\r\n    if (userinfo) {\r\n      if (userinfo.checked) {\r\n        this.checked = userinfo.checked\r\n        this.loginForm.account = userinfo.account\r\n        this.loginForm.password = this.$utils.decrypt(userinfo.password, 'abcdefgabcdefg12')\r\n      }\r\n    }\r\n    this.getverify()\r\n  },\r\n  watch: {\r\n    loginTab (val) {\r\n      if (val === '2') {\r\n        this.refresh()\r\n      } else if (val === '1') {\r\n        this.disabled = false\r\n        clearTimeout(this.setTimeout)\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    btnback () {\r\n      // const routeData = this.$router.resolve({ path: '/login-help' })\r\n      window.open('#/login-help', '_blank')\r\n    },\r\n    async getverify () {\r\n      const res = await this.$api.general.shortcodeEnable()\r\n      var { data } = res\r\n      this.showVrify = data\r\n    },\r\n    async sendCode (formName, num) {\r\n      this.$refs[formName].validate(async (valid) => {\r\n        console.log('valid===>', valid)\r\n        if (num === 1) {\r\n          const res = await this.$api.general.shortcodeSend({\r\n            account: this.loginForm.account,\r\n            password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002')\r\n          })\r\n          var { errcode } = res\r\n          if (errcode === 200) {\r\n            this.setTimeoutNum = 60\r\n            setInterval(() => {\r\n              if (this.setTimeoutNum > 0) this.setTimeoutNum--\r\n            }, 1000)\r\n            this.$message({\r\n              message: '验证码已发送，请注意查收！',\r\n              type: 'success'\r\n            })\r\n          }\r\n        } else {\r\n          if (valid) {\r\n            const res = await this.$api.general.shortcodeSend({\r\n              account: this.loginForm.account,\r\n              password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002')\r\n            })\r\n            var { errcodes } = res\r\n            if (errcodes === 200) {\r\n              this.setTimeoutNum = 60\r\n              setInterval(() => {\r\n                if (this.setTimeoutNum > 0) this.setTimeoutNum--\r\n              }, 1000)\r\n              this.$message({\r\n                message: '验证码已发送，请注意查收！',\r\n                type: 'success'\r\n              })\r\n            }\r\n          } else {\r\n            return false\r\n          }\r\n        }\r\n      })\r\n    },\r\n    async loginimg () {\r\n      const res = await this.$api.general.loginimg()\r\n      var { data } = res\r\n      this.loginImgList = data\r\n    },\r\n    gologin () {\r\n      this.forgetflag = false\r\n    },\r\n    onSuccess (times) {\r\n      // console.log('验证通过')\r\n      this.valid = true\r\n      this.disabled = true\r\n    },\r\n    onAgain () {\r\n      this.$message.error('检测到非人为操作的哦！')\r\n      this.handleClick()\r\n    },\r\n    handleClick () {\r\n      this.disabled = false\r\n      this.$refs.slideblock.reset()\r\n    },\r\n    switchMethods (type) {\r\n      if (this.switchLogin === type) {\r\n        return\r\n      }\r\n      this.switchLogin = type\r\n      if (!this.switchLogin) {\r\n        this.refresh()\r\n      } else {\r\n        this.disabled = false\r\n        sessionStorage.clear()\r\n        clearTimeout(this.setTimeout)\r\n      }\r\n    },\r\n    async refresh () {\r\n      const res = await this.$api.general.nologin({\r\n        codes: 'rongCloudIdPrefix'\r\n      })\r\n      console.log(res)\r\n      var { data } = res\r\n      this.time = false\r\n      this.uuid = uuidv1()\r\n      this.text = data.rongCloudIdPrefix + '|login|' + this.uuid\r\n      this.apptoken()\r\n      this.setTimeout = setTimeout(() => {\r\n        this.time = true\r\n      }, 180000)\r\n    },\r\n    async apptoken () {\r\n      const res = await this.$api.general.apptoken({\r\n        qrCodeId: this.uuid\r\n      })\r\n      if (res.data === '' && this.loginTab === '2' && !this.time) {\r\n        setTimeout(() => {\r\n          this.apptoken()\r\n        }, 2000)\r\n      }\r\n      if (res.data) {\r\n        this.qrloading = true\r\n        clearTimeout(this.setTimeout)\r\n        this.time = true\r\n        var { data, projects } = res\r\n        this.$message({\r\n          message: '登录成功!',\r\n          type: 'success'\r\n        })\r\n        sessionStorage.setItem('token' + this.$logo(), JSON.stringify(data))\r\n        sessionStorage.setItem('projects' + this.$logo(), JSON.stringify(projects))\r\n        if (this.$isMoreProject()) {\r\n          if (projects.length === 1) {\r\n            this.$switchClick(projects[0].hostAddr)\r\n          } else {\r\n            this.$router.push({ path: '/switchpage' })\r\n          }\r\n        } else {\r\n          if (projects.length) {\r\n            projects.forEach(item => {\r\n              if (item.hostAddr === this.$api.general.baseURL()) {\r\n                console.log(item.hostAddr)\r\n                this.$switchClick(item.hostAddr)\r\n              }\r\n            })\r\n          } else {\r\n            this.$switchClick(this.$api.general.baseURL())\r\n          }\r\n        }\r\n      }\r\n    },\r\n    success () {\r\n      this.valid = true\r\n    },\r\n    submitForm (formName) {\r\n      this.rules.verify.push({ required: true, message: '请输入验证码', trigger: 'blur' })\r\n      this.$refs[formName].validate(async (valid) => {\r\n        if (valid) {\r\n          // if (this.showVrify) {\r\n          //   const res2 = await this.$api.general.shortcodeVerify({\r\n          //     account: this.loginForm.account,\r\n          //     verifyCode: this.loginForm.verify\r\n          //   })\r\n          //   const { errcode } = res2\r\n          //   if (errcode !== 200) {\r\n          //     this.$message.error(errcode)\r\n          //     return\r\n          //   }\r\n          // }\r\n          this.loading = true\r\n          if (!this.valid && !this.showVrify) {\r\n            this.loading = false\r\n            this.$message.error('请先通过验证在登录!')\r\n            return\r\n          }\r\n          this.$api.general.loginUc({\r\n            username: this.loginForm.account,\r\n            password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002'),\r\n            verifyCode: this.loginForm.verify\r\n          }).then(res => {\r\n            var { data, projects } = res\r\n            this.$message({\r\n              message: '登录成功!',\r\n              type: 'success'\r\n            })\r\n            sessionStorage.setItem('token' + this.$logo(), JSON.stringify(data))\r\n            sessionStorage.setItem('projects' + this.$logo(), JSON.stringify(projects))\r\n            if (this.$isMoreProject()) {\r\n              if (projects.length === 1) {\r\n                this.$switchClick(projects[0].hostAddr)\r\n              } else {\r\n                this.$router.push({ path: '/switchpage' })\r\n              }\r\n            } else {\r\n              if (projects.length) {\r\n                projects.forEach(item => {\r\n                  if (item.hostAddr === this.$api.general.baseURL()) {\r\n                    console.log(item.hostAddr)\r\n                    this.$switchClick(item.hostAddr)\r\n                  }\r\n                })\r\n              } else {\r\n                this.$switchClick(this.$api.general.baseURL())\r\n              }\r\n            }\r\n            const userinfo = { checked: this.checked, account: this.loginForm.account, password: this.$utils.encrypt(this.loginForm.password, 'abcdefgabcdefg12') }\r\n            localStorage.setItem('userinfo' + this.$logo(), JSON.stringify(userinfo))\r\n          }).catch(() => {\r\n            this.loading = false\r\n            this.$message.error('登录失败!')\r\n          })\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    },\r\n    browser (url) {\r\n      window.open(url, '_blank')\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n@import \"./login-qdzx.scss\";\r\n</style>\r\n"]}]}