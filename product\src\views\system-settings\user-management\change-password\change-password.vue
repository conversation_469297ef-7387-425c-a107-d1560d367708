<template>
  <div class="change-password">
    <el-form :model="form" :rules="rules" inline ref="form" label-position="top" class="newForm">
      <el-form-item label="新密码（8位以上，大小写字母、数字、符号至少包含3种）" class="form-input" prop="pass">
        <el-input placeholder="请输入密码" type="password" v-model="form.pass" clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="确认密码" class="form-input" prop="checkPass">
        <el-input placeholder="请输入密码" type="password" v-model="form.checkPass" clearable>
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary" @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'changePassword',
  data () {
    var validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        // 密码验证：8位以上，大小写字母、数字、符号至少包含3种
        if (value.length < 8) {
          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))
          return
        }

        let typeCount = 0

        // 检查是否包含小写字母
        if (/[a-z]/.test(value)) {
          typeCount++
        }

        // 检查是否包含大写字母
        if (/[A-Z]/.test(value)) {
          typeCount++
        }

        // 检查是否包含数字
        if (/[0-9]/.test(value)) {
          typeCount++
        }

        // 检查是否包含符号
        if (/[~!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value)) {
          typeCount++
        }

        if (typeCount >= 3) {
          if (this.form.checkPass !== '') {
            this.$refs.form.validateField('checkPass')
          }
          callback()
        } else {
          callback(new Error('密码至少在8位以上，大小写字母、数字、符号至少包含3种'))
        }
      }
    }
    var validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.pass) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      form: {
        pass: '',
        checkPass: ''
      },
      rules: {
        pass: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ],
        checkPass: [
          { required: true, validator: validatePass2, trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id'],
  methods: {
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/wholeuser/editpwd'
          this.$api.systemSettings.generalAdd(url, {
            userId: this.id,
            password: this.$utils.encrypt(this.form.checkPass, 'zysofthnzx202002')
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('Callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('Callback')
    }
  }
}
</script>
<style lang="scss">
.change-password {
  width: 692px;
  height: 100%;
  padding: 24px 40px;
}
</style>
