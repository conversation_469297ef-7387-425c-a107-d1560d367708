<template>
  <div class="login-qdzx">
    <el-carousel class="login-qdzxCarousel" arrow="never">
      <el-carousel-item v-for="item in loginImgList" :key="item.id">
        <div class="loginTwoImg"
          :style="`background: url('${item.rollImgUrl}') no-repeat;background-size: cover;background-position: top;`">
        </div>
      </el-carousel-item>
    </el-carousel>
    <div class="login-qdzx-box">
      <div class="login-qdzx-boxs">
        <div class="login-qdzx-logo-box">
          <div class="login-qdzx-logo"></div>
          <div class="login-qdzx-name">{{ $generalName() }}</div>
        </div>
        <div class="login-qdzx-login-box">
          <xyl-sliding v-model="loginTab" v-if="!forgetflag">
            <xyl-sliding-item value="1">账号登录</xyl-sliding-item>
            <xyl-sliding-item value="2">扫码登录</xyl-sliding-item>
          </xyl-sliding>
          <div class="login-qdzx-form-box" v-if="loginTab == '1' && !forgetflag">
            <el-form :model="loginForm" :rules="rules" class="loginzx-form" ref="loginForm" @submit.native.prevent>
              <el-form-item prop="account" class="loginzx-form-input">
                <el-input v-model="loginForm.account" placeholder="账号/手机号/证件号码" clearable></el-input>
              </el-form-item>
              <el-form-item prop="password" class="loginzx-form-input">
                <el-input type="password" show-password v-model="loginForm.password" placeholder="密码" clearable
                  @keyup.enter.native="submitForm('loginForm')"></el-input>
              </el-form-item>
              <el-form-item v-if="showVrify" prop="verify" class="loginzx-form-input">
                <el-row :gutter="20">
                  <el-col :span="14">
                    <el-input v-model="loginForm.verify" placeholder="验证码" clearable
                      @keyup.enter.native="submitForm('loginForm')">
                    </el-input>
                  </el-col>
                  <el-col :span="10">
                    <el-button :disabled="setTimeoutNum ? true : false" style="width: 100%" round type="primary"
                      @click="sendCode('loginForm', 1)">
                      <span v-if="setTimeoutNum">{{ setTimeoutNum }}</span>
                      发送验证码
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
              <div class="form-slide-verify" v-if="!showVrify">
                <slide-verify ref="slideblock" :w="336" :ws="21" @success="onSuccess" @again="onAgain"
                  :disabled="disabled" slider-text="请拖动滑块至正确缺口"></slide-verify>
              </div>
              <div class="loginzx-form-operation">
                <el-checkbox v-model="checked">记住用户名和密码</el-checkbox>
                <div class="loginzx-form-operation-box">
                  <div class="loginzx-form-operation-text" @click="forgetflag = true">
                    忘记密码？
                  </div>
                  <!-- <div class="loginzx-form-operation-l"></div>
                <div class="loginzx-form-operation-text">修改密码</div> -->
                </div>
              </div>
              <el-button type="primary" class="loginzx-form-button" :loading="loading"
                @click="submitForm('loginForm')">{{ loading ? '登录中' : '登录' }}</el-button>
            </el-form>
          </div>
          <div class="login-qdzx-qr-box" v-if="loginTab == '2' && !forgetflag">
            <div class="qr-code-box" v-loading="qrloading">
              <vue-qr class="qr-code qrcode" :margin="0" :text="text"></vue-qr>
              <div class="refresh" v-if="time && !qrloading">
                <div class="refresh-button">
                  <el-button type="primary" size="small" @click="refresh">刷新</el-button>
                </div>
              </div>
            </div>
            <div class="qr-code-box-text">手机APP扫码登录</div>
            <div class="qr-code-box-text-c">扫码登录，更快，更安全</div>
          </div>
          <div v-if="forgetflag">
            <forgetpassword @gologin="gologin"></forgetpassword>
          </div>
        </div>
      </div>
      <div class="login-qdzx-text-box">
        <div class="login-qdzx-text">
          本系统仅用于非涉及党和国家秘密信息处理
        </div>
        <div class="login-qdzx-text">技术支持:135 7313 9687</div>
      </div>
    </div>
    <div class="inform" v-if="isShowIE">
      <div class="informBox">
        <div class="informName">关于系统浏览器使用说明</div>
        <div class="informText">尊敬的青岛智慧政协平台用户：</div>
        <div class="informText">您好！青岛市智慧政协平台升级版正式上线。</div>
        <div class="informText">
          由于微软公司推出的IE浏览器存在大量漏洞，且微软公司已停止对IE系列浏览器进行维护升级，在使用过程中存在信息安全隐患。为此本系统将不再兼容IE系列浏览器。
        </div>
        <div class="informText">
          为了您更好的使用体验及信息安全，建议优先选用国产浏览器及国际稳定版浏览器使用，推荐浏览器如下：火狐浏览器、360浏览器（使用极速模式）、Edge浏览器（win10自带浏览器）、谷歌浏览器等。
        </div>
        <div class="informText">
          如有任何疑问及使用问题，请致电信息化工作处0532-82937892或技术人员电话17854218732
        </div>
        <div class="downloadText">
          如本机尚未安装上述浏览器 可点击以下按钮自行下载
        </div>
        <div class="downloadBox">
          <el-button type="primary" @click="btnback">请点击此处跳转至下载中心！</el-button>
          <!-- <div class="downloadItem"
               @click="browser('http://www.firefox.com.cn/')">
            <div class="downloadIcon"></div>
            火狐浏览器
          </div>
          <div class="downloadItem"
               @click="browser('https://browser.360.cn/ee/')">
            <div class="downloadIcon"></div>
            360浏览器
          </div>
          <div class="downloadItem"
               @click="browser('https://www.microsoft.com/zh-cn/edge')">
            <div class="downloadIcon"></div>
            Edge浏览器
          </div>
          <div class="downloadItem"
               @click="browser('https://www.google.cn/chrome/thank-you.html?installdataindex=empty&statcb=1&defaultbrowser=0')">
            <div class="downloadIcon"></div>
            谷歌浏览器
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { v1 as uuidv1 } from 'uuid'
import xylSliding from '@/components/zy-sliding/sliding'
import xylSlidingItem from '@/components/zy-sliding/sliding-item'
import vueQr from 'vue-qr'
import slideVerify from '@/components/slide-verify/slide-verify'
import forgetpassword from '../forgetPassword/forgetPassword'
export default {
  name: 'login',
  data () {
    return {
      loginImgList: [],
      loginTab: '1',
      forgetflag: false,
      switchLogin: true,
      checked: false,
      valid: false,
      disabled: false,
      loginForm: {
        account: '',
        password: '',
        verify: ''
      },
      rules: {
        account: [
          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        verify: []
      },
      loading: false,
      text: '',
      uuid: '',
      time: false,
      qrloading: false,
      setTimeout: null,
      isShowIE: false,
      showVrify: false,
      setTimeoutNum: 0
    }
  },
  components: {
    xylSliding,
    xylSlidingItem,
    vueQr,
    slideVerify,
    forgetpassword
  },
  created () {
    if (!!window.ActiveXObject || 'ActiveXObject' in window) {
      this.isShowIE = true
    } else {
      this.isShowIE = false
    }
    this.loginimg()
    var userinfo = JSON.parse(localStorage.getItem('userinfo' + this.$logo())) || ''
    if (userinfo) {
      if (userinfo.checked) {
        this.checked = userinfo.checked
        this.loginForm.account = userinfo.account
        this.loginForm.password = this.$utils.decrypt(userinfo.password, 'abcdefgabcdefg12')
      }
    }
    this.getverify()
  },
  watch: {
    loginTab (val) {
      if (val === '2') {
        this.refresh()
      } else if (val === '1') {
        this.disabled = false
        clearTimeout(this.setTimeout)
      }
    }
  },
  methods: {
    btnback () {
      // const routeData = this.$router.resolve({ path: '/login-help' })
      window.open('#/login-help', '_blank')
    },
    async getverify () {
      const res = await this.$api.general.shortcodeEnable()
      var { data } = res
      this.showVrify = data
    },
    async sendCode (formName, num) {
      this.$refs[formName].validate(async (valid) => {
        console.log('valid===>', valid)
        if (num === 1) {
          const res = await this.$api.general.shortcodeSend({
            account: this.loginForm.account,
            password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002')
          })
          var { errcode } = res
          if (errcode === 200) {
            this.setTimeoutNum = 60
            setInterval(() => {
              if (this.setTimeoutNum > 0) this.setTimeoutNum--
            }, 1000)
            this.$message({
              message: '验证码已发送，请注意查收！',
              type: 'success'
            })
          }
        } else {
          if (valid) {
            const res = await this.$api.general.shortcodeSend({
              account: this.loginForm.account,
              password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002')
            })
            var { errcodes } = res
            if (errcodes === 200) {
              this.setTimeoutNum = 60
              setInterval(() => {
                if (this.setTimeoutNum > 0) this.setTimeoutNum--
              }, 1000)
              this.$message({
                message: '验证码已发送，请注意查收！',
                type: 'success'
              })
            }
          } else {
            return false
          }
        }
      })
    },
    async loginimg () {
      const res = await this.$api.general.loginimg()
      var { data } = res
      this.loginImgList = data
    },
    gologin () {
      this.forgetflag = false
    },
    onSuccess (times) {
      // console.log('验证通过')
      this.valid = true
      this.disabled = true
    },
    onAgain () {
      this.$message.error('检测到非人为操作的哦！')
      this.handleClick()
    },
    handleClick () {
      this.disabled = false
      this.$refs.slideblock.reset()
    },
    switchMethods (type) {
      if (this.switchLogin === type) {
        return
      }
      this.switchLogin = type
      if (!this.switchLogin) {
        this.refresh()
      } else {
        this.disabled = false
        sessionStorage.clear()
        clearTimeout(this.setTimeout)
      }
    },
    async refresh () {
      const res = await this.$api.general.nologin({
        codes: 'rongCloudIdPrefix'
      })
      console.log(res)
      var { data } = res
      this.time = false
      this.uuid = uuidv1()
      this.text = data.rongCloudIdPrefix + '|login|' + this.uuid
      this.apptoken()
      this.setTimeout = setTimeout(() => {
        this.time = true
      }, 180000)
    },
    async apptoken () {
      const res = await this.$api.general.apptoken({
        qrCodeId: this.uuid
      })
      if (res.data === '' && this.loginTab === '2' && !this.time) {
        setTimeout(() => {
          this.apptoken()
        }, 2000)
      }
      if (res.data) {
        this.qrloading = true
        clearTimeout(this.setTimeout)
        this.time = true
        var { data, projects } = res
        this.$message({
          message: '登录成功!',
          type: 'success'
        })
        sessionStorage.setItem('token' + this.$logo(), JSON.stringify(data))
        sessionStorage.setItem('projects' + this.$logo(), JSON.stringify(projects))
        if (this.$isMoreProject()) {
          if (projects.length === 1) {
            this.$switchClick(projects[0].hostAddr)
          } else {
            this.$router.push({ path: '/switchpage' })
          }
        } else {
          if (projects.length) {
            projects.forEach(item => {
              if (item.hostAddr === this.$api.general.baseURL()) {
                console.log(item.hostAddr)
                this.$switchClick(item.hostAddr)
              }
            })
          } else {
            this.$switchClick(this.$api.general.baseURL())
          }
        }
      }
    },
    success () {
      this.valid = true
    },
    submitForm (formName) {
      this.rules.verify.push({ required: true, message: '请输入验证码', trigger: 'blur' })
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // if (this.showVrify) {
          //   const res2 = await this.$api.general.shortcodeVerify({
          //     account: this.loginForm.account,
          //     verifyCode: this.loginForm.verify
          //   })
          //   const { errcode } = res2
          //   if (errcode !== 200) {
          //     this.$message.error(errcode)
          //     return
          //   }
          // }
          this.loading = true
          if (!this.valid && !this.showVrify) {
            this.loading = false
            this.$message.error('请先通过验证在登录!')
            return
          }
          this.$api.general.loginUc({
            username: this.loginForm.account,
            password: this.$utils.encrypt(this.loginForm.password, 'zysofthnzx202002'),
            verifyCode: this.loginForm.verify
          }).then(res => {
            var { data, projects } = res
            this.$message({
              message: '登录成功!',
              type: 'success'
            })
            sessionStorage.setItem('token' + this.$logo(), JSON.stringify(data))
            sessionStorage.setItem('projects' + this.$logo(), JSON.stringify(projects))
            if (this.$isMoreProject()) {
              if (projects.length === 1) {
                this.$switchClick(projects[0].hostAddr)
              } else {
                this.$router.push({ path: '/switchpage' })
              }
            } else {
              if (projects.length) {
                projects.forEach(item => {
                  if (item.hostAddr === this.$api.general.baseURL()) {
                    console.log(item.hostAddr)
                    this.$switchClick(item.hostAddr)
                  }
                })
              } else {
                this.$switchClick(this.$api.general.baseURL())
              }
            }
            const userinfo = { checked: this.checked, account: this.loginForm.account, password: this.$utils.encrypt(this.loginForm.password, 'abcdefgabcdefg12') }
            localStorage.setItem('userinfo' + this.$logo(), JSON.stringify(userinfo))
          }).catch(() => {
            this.loading = false
            this.$message.error('登录失败!')
          })
        } else {
          return false
        }
      })
    },
    browser (url) {
      window.open(url, '_blank')
    }
  }
}
</script>
<style lang="scss">
@import "./login-qdzx.scss";
</style>
