{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\forgetPassword\\forgetPassword.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\main\\forgetPassword\\forgetPassword.vue", "mtime": 1755833016358}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["forgetPassword.vue"], "names": [], "mappings": ";AAiDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "forgetPassword.vue", "sourceRoot": "src/views/main/forgetPassword", "sourcesContent": ["<template>\r\n  <div class=\"login-form-box-pass\">\r\n    <div class=\"forgetText\">密码至少在8位以上，大小写字母、数字、符号至少包含3种。</div>\r\n    <el-form :model=\"forgetForm\" :rules=\"forgetrules\" class=\"login-form\" ref=\"forgetForm\" @submit.native.prevent>\r\n      <el-form-item prop=\"account\" class=\"login-form-input\">\r\n        <el-input v-model=\"forgetForm.account\" placeholder=\"账号/手机号/证件号码\" clearable></el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item class=\"login-form-input form-valid\">\r\n        <el-input v-model=\"forgetForm.validCode\" placeholder=\"验证码\" clearable> </el-input>\r\n        <valid :length=\"4\" :heigth=\"'36px'\" :value.sync=\"validCode\"></valid>\r\n      </el-form-item>\r\n      <!-- <div class=\"form-valid\">\r\n        <el-input\r\n          v-model=\"forgetForm.validCode\"\r\n          placeholder=\"验证码\"\r\n          clearable\r\n        > </el-input>\r\n        <valid\r\n          :length=\"4\"\r\n          :heigth=\"'36px'\"\r\n          :value.sync=\"validCode\"\r\n        ></valid>\r\n      </div> -->\r\n      <el-form-item prop=\"newPassword\" class=\"login-form-input\">\r\n\r\n        <el-input type=\"password\" v-model=\"forgetForm.newPassword\" placeholder=\"请输入新密码\" clearable></el-input>\r\n      </el-form-item>\r\n\r\n      <el-form-item prop=\"newPassword\" class=\"login-form-input\">\r\n\r\n        <el-input type=\"password\" v-model=\"forgetForm.newPasswordAgian\" placeholder=\"请再次输入新密码\" clearable></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"login-form-input form-valid\">\r\n        <el-input v-model=\"forgetForm.smsValidation\" placeholder=\"短信验证码\" clearable> </el-input>\r\n        <el-button type=\"primary\" @click=\"validCodetesting\" :disabled=\"smsText != '获取验证码'\"> {{ smsText }} </el-button>\r\n      </el-form-item>\r\n      <div class=\"login-form-operation\">\r\n        <div class=\"login-form-operation-box\">\r\n          <div class=\"login-form-operation-text\" @click=\"gologin\">返回登录</div>\r\n        </div>\r\n      </div>\r\n      <el-button type=\"primary\" class=\"login-form-button\" @click=\"editpwd('forgetForm')\"> 确定 </el-button>\r\n    </el-form>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport valid from '@/components/valid/valid'\r\nexport default {\r\n  components: { valid },\r\n  data () {\r\n    return {\r\n      validCode: '',\r\n      forgetForm: {\r\n        account: '',\r\n        validCode: '',\r\n        smsValidation: '',\r\n        newPassword: '',\r\n        newPasswordAgian: ''\r\n      },\r\n      forgetrules: {\r\n        account: [\r\n          { required: true, message: '请输入账号/手机号/证件号码', trigger: 'blur' }\r\n        ],\r\n        newPassword: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        newPasswordAgian: [\r\n          { required: true, message: '请输入密码', trigger: 'blur' }\r\n        ],\r\n        smsValidation:\r\n          [{ required: true, message: '请输入短信验证码', trigger: 'blur' }]\r\n      },\r\n      smsShow: false,\r\n      smsText: '获取验证码',\r\n      countdown: 0\r\n    }\r\n  },\r\n  methods: {\r\n    // 密码验证函数：8位以上，大小写字母、数字、符号至少包含3种\r\n    validatePassword (password) {\r\n      if (password.length < 8) {\r\n        return false\r\n      }\r\n\r\n      let typeCount = 0\r\n\r\n      // 检查是否包含小写字母\r\n      if (/[a-z]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含大写字母\r\n      if (/[A-Z]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含数字\r\n      if (/[0-9]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      // 检查是否包含符号\r\n      if (/[~!@#$%^&*()_+\\-=[\\]{};':\"\\\\|,.<>/?]/.test(password)) {\r\n        typeCount++\r\n      }\r\n\r\n      return typeCount >= 3\r\n    },\r\n\r\n    forget () {\r\n      // this.$router.push({ name: 'changepassword' })\r\n      this.forgetflag = true\r\n      this.forgetForm.account = this.loginForm.account\r\n      this.loginForm.smsValidation = ''\r\n      this.countdown = 0\r\n    },\r\n    gologin () {\r\n      // this.countdown = 0\r\n      // this.forgetflag = false\r\n      this.$emit('gologin')\r\n    },\r\n    validCodetesting () {\r\n      if (this.forgetForm.account === '') {\r\n        this.$message({\r\n          message: '请先输入账号/手机号/证件号码！',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {\r\n        this.forgetForm.validCode = ''\r\n        this.$message.warning('验证码不正确!')\r\n        return\r\n      }\r\n      if (!this.validatePassword(this.forgetForm.newPassword)) {\r\n        this.$message({\r\n          message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n\r\n      if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {\r\n        this.$message({\r\n          message: '两次密码输入不一致！',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      this.$api.general.sendmessage({\r\n        account: this.forgetForm.account,\r\n        password: ''\r\n      }).then(res => {\r\n        this.countdown = 60\r\n        this.settime()\r\n        this.$message({\r\n          message: '短信发送成功!',\r\n          type: 'success'\r\n        })\r\n      })\r\n    },\r\n    editpwd (formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (valid) {\r\n          if (this.forgetForm.validCode.toLowerCase() !== this.validCode.toLowerCase()) {\r\n            this.forgetForm.validCode = ''\r\n            this.$message.warning('验证码不正确!')\r\n            return\r\n          }\r\n          if (!this.validatePassword(this.forgetForm.newPassword)) {\r\n            this.$message({\r\n              message: '密码至少在8位以上，大小写字母、数字、符号至少包含3种。',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n\r\n          if (this.forgetForm.newPasswordAgian !== this.forgetForm.newPassword) {\r\n            this.$message({\r\n              message: '两次密码输入不一致！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n          if (this.forgetForm.smsValidation === '') {\r\n            this.$message({\r\n              message: '请输入验证码！',\r\n              type: 'warning'\r\n            })\r\n            return\r\n          }\r\n          this.$api.general.editpwd({\r\n            username: this.forgetForm.account,\r\n            password: this.$utils.encrypt(this.forgetForm.newPassword, 'zysofthnzx202002'),\r\n            verifyCode: this.forgetForm.smsValidation\r\n          })\r\n        }\r\n      })\r\n    },\r\n    settime () {\r\n      if (this.countdown === 0) {\r\n        this.smsText = '获取验证码'\r\n        this.countdown = 60\r\n        return\r\n      } else {\r\n        this.smsText = '重新发送' + this.countdown + 'S'\r\n        this.countdown--\r\n      }\r\n      setTimeout(() => {\r\n        this.settime()\r\n      }, 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scpoed>\r\n// @import './login.scss';\r\n.forgetText {\r\n  font-size: $textSize12;\r\n  line-height: 20px;\r\n}\r\n\r\n.login-form-box-pass {\r\n  padding: 24px;\r\n  min-height: 326px;\r\n}\r\n\r\n.login-form {\r\n  padding-top: 12px;\r\n\r\n  .login-form-input {\r\n    margin-bottom: 19px;\r\n\r\n    .el-form-item__content {\r\n      line-height: 36px;\r\n\r\n      .el-input__inner {\r\n        height: 36px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-valid {\r\n    width: 100%;\r\n    height: 36px;\r\n    margin-bottom: 19px;\r\n\r\n    .el-form-item__content {\r\n      display: flex;\r\n    }\r\n\r\n    .el-input {\r\n      width: 154px;\r\n      height: 36px;\r\n\r\n      input {\r\n        height: 36px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n\r\n    .ValidCode {\r\n      background-color: #ccc;\r\n      margin-left: 20px;\r\n    }\r\n\r\n    .el-button {\r\n      margin-left: 20px;\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .login-form-operation {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    margin-bottom: 24px;\r\n\r\n    .login-form-operation-box {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n\r\n      .login-form-operation-text {\r\n        font-size: $textSize12;\r\n        line-height: 14px;\r\n        font-family: SimSun;\r\n        color: $zy-color;\r\n        font-weight: 400;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .login-form-operation-l {\r\n        width: 1px;\r\n        height: 14px;\r\n        margin: 0 9px;\r\n        background-color: #e4dee0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .login-form-button {\r\n    width: 100%;\r\n    background-color: $zy-color;\r\n    padding: 0;\r\n    height: 38px;\r\n    line-height: 38px;\r\n  }\r\n}\r\n\r\n.form-valid {\r\n  width: 100%;\r\n  height: 36px;\r\n  margin-bottom: 19px;\r\n\r\n  .el-form-item__content {\r\n    display: flex;\r\n  }\r\n\r\n  .el-input {\r\n    width: 154px;\r\n    height: 36px;\r\n\r\n    input {\r\n      height: 36px;\r\n      line-height: 36px;\r\n    }\r\n  }\r\n\r\n  .ValidCode {\r\n    background-color: #ccc;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .el-button {\r\n    margin-left: 20px;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n"]}]}